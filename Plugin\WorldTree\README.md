# 世界树角色管理系统 (WorldTree)

## 概述

世界树角色管理系统是一个强大的VCP插件，用于管理AI角色的时间架构、日程表和心理活动。当主程序server.js处理请求时，如果使用了配置的角色名称，系统会自动将相关的角色设定、日程安排和心理状态注入到system消息中，大大增强AI的拟人性和一致性。

## 主要功能

### 🎭 角色管理
- 创建和管理多个AI角色
- 为每个角色设定独特的时间架构和背景
- 支持角色描述和个性特征配置

### 📅 日程表系统
- 为角色创建日常、周期性或特殊事件日程
- 支持多种日程类型：daily、weekly、monthly、event
- 自动根据时间显示当前相关的日程安排

### 🧠 心理活动生成
- 集成AdvancedMemorySystem数据
- 使用配置的AI API生成角色心理活动
- 根据对话上下文、记忆数据和角色状态生成真实的内心活动

### 🔄 动态System消息注入
- 在VCP和MCP模式下自动注入角色上下文
- 根据assistantName匹配对应角色
- 实时更新角色状态和心理活动

## 安装配置

### 1. 环境变量配置

在主配置文件或环境变量中添加以下配置：

```env
# 世界树插件配置
WORLDTREE_DEBUG_MODE=false
WORLDTREE_AI_API_URL=https://api.openai.com/v1
WORLDTREE_AI_API_KEY=your_openai_api_key_here
WORLDTREE_AI_MODEL=gpt-4o-mini
WORLDTREE_PSYCHOLOGY_ENABLED=true
WORLDTREE_MAX_PSYCHOLOGY_LENGTH=500
WORLDTREE_SCHEDULE_INTERVAL=60
```

### 2. 插件配置文件

复制 `config.env.example` 为 `config.env` 并填入实际配置：

```bash
cp Plugin/WorldTree/config.env.example Plugin/WorldTree/config.env
```

### 3. 数据库初始化

插件会自动创建SQLite数据库和必要的表结构，无需手动操作。

## 使用方法

### 1. 创建角色

使用VCP工具调用创建新角色：

```json
{
  "action": "create_character",
  "character_name": "雨安安",
  "time_structure": "现代都市设定，2024年12月，大学生",
  "description": "活泼开朗的大学生，喜欢动漫和编程"
}
```

### 2. 添加日程

为角色添加日程安排：

```json
{
  "action": "add_schedule",
  "character_name": "雨安安",
  "schedule_type": "daily",
  "title": "上课",
  "description": "计算机科学课程",
  "start_time": "2024-12-19T09:00:00Z",
  "end_time": "2024-12-19T11:00:00Z",
  "priority": 2
}
```

### 3. 更新角色状态

更新角色的心情和状态：

```json
{
  "action": "update_character_state",
  "character_name": "雨安安",
  "current_mood": "开心",
  "energy_level": 0.8,
  "stress_level": 0.3,
  "active_goals": ["完成作业", "准备考试"]
}
```

### 4. 生成心理活动

手动触发心理活动生成：

```json
{
  "action": "generate_psychology",
  "character_name": "雨安安",
  "conversation_context": "用户询问关于学习的问题",
  "memory_data": {}
}
```

## API接口

### 获取角色列表
```json
{
  "action": "get_characters"
}
```

### 获取角色上下文
```json
{
  "action": "get_character_context",
  "character_name": "雨安安",
  "include_schedule": true,
  "include_psychology": true
}
```

### 获取系统消息
```json
{
  "action": "get_system_message",
  "character_name": "雨安安"
}
```

## 自动集成

### System消息注入

当server.js处理请求时，如果请求中的`assistantName`与数据库中的角色名称匹配，系统会自动：

1. 获取角色的基本信息和时间架构
2. 查询当前相关的日程安排
3. 获取最新的心理活动状态
4. 将这些信息格式化后注入到system消息的最前面

### 心理活动自动生成

系统会根据以下因素自动生成角色心理活动：
- 最近的对话记录
- AdvancedMemorySystem的情感和记忆数据
- 角色当前状态和日程
- 角色的个性特征和背景设定

## 数据库结构

### characters 表
- 存储角色基本信息
- 包含名称、时间架构、描述等

### schedules 表
- 存储角色日程安排
- 支持多种日程类型和重复规则

### psychology_activities 表
- 记录角色心理活动历史
- 包含思考、情绪、记忆、反应等类型

### character_states 表
- 存储角色当前状态
- 包含心情、精力、压力、目标等

## 注意事项

1. **角色名称匹配**：确保请求中的`assistantName`与数据库中的角色名称完全一致
2. **API密钥配置**：心理活动生成功能需要配置有效的AI API密钥
3. **性能考虑**：系统消息有5分钟缓存，避免频繁生成
4. **数据备份**：定期备份`data/world_tree.db`数据库文件

## 故障排除

### 常见问题

1. **角色上下文未注入**
   - 检查角色名称是否正确
   - 确认插件是否正确初始化
   - 查看日志中的错误信息

2. **心理活动生成失败**
   - 检查AI API配置是否正确
   - 确认API密钥是否有效
   - 查看网络连接是否正常

3. **数据库错误**
   - 检查数据库文件权限
   - 确认SQLite3模块是否正确安装
   - 查看数据库文件是否损坏

### 调试模式

启用调试模式获取详细日志：

```env
WORLDTREE_DEBUG_MODE=true
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持角色管理和日程表功能
- 集成心理活动生成系统
- 实现自动system消息注入
