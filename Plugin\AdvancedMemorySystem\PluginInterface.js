const EmotionMemorySystem = require('./EmotionMemorySystem');
const path = require('path');
const fs = require('fs');
const logger = require('../../utils/logger.cjs');

/**
 * 智能情感记忆系统插件接口
 * 实现异步处理和logger日志记录
 */
class AdvancedMemorySystemPlugin {
    constructor() {
        this.system = null;
        this.isInitialized = false;
        this.logger = logger;
        this.config = null;
        this.transactionMap = new Map(); // 事务映射
    }

    /**
     * 插件初始化
     */
    async initialize(config = {}) {
        try {
            // 加载配置
            this.config = this.loadConfig(config);

            this.logger.memory('开始初始化智能情感记忆系统插件');

            // 初始化核心系统（传递正确的参数）
            this.system = new EmotionMemorySystem(__dirname, this.logger);
            await this.system.initialize();

            // 注册配置热更新回调
            this.registerConfigHotReload();

            // 获取核心服务的引用，用于外部访问
            this.openaiService = this.system.openaiService;
            this.embeddingService = this.system.embeddingService;
            this.conversationManager = this.system.conversationManager;
            this.intelligentPsychAnalysis = this.system.intelligentPsychAnalysis;
            
            // 注意：generateIntelligentContext已经作为类方法定义，无需额外绑定

            // 调试：检查核心服务是否正确初始化
            if (!this.openaiService) {
                this.logger.error('插件初始化', 'openaiService未正确初始化');
            } else if (!this.openaiService.analyzeUserPositivity) {
                this.logger.error('插件初始化', 'openaiService缺少analyzeUserPositivity方法');
            } else {
                this.logger.info('插件初始化', 'openaiService初始化成功，包含所需方法');
            }

            if (!this.embeddingService) {
                this.logger.error('插件初始化', 'embeddingService未正确初始化');
            } else {
                this.logger.info('插件初始化', 'embeddingService初始化成功');
            }

            if (!this.conversationManager) {
                this.logger.error('插件初始化', 'conversationManager未正确初始化');
            } else {
                this.logger.info('插件初始化', 'conversationManager初始化成功');
            }

            this.isInitialized = true;
            this.logger.success('插件初始化', '智能情感记忆系统插件初始化成功');
            
            return {
                success: true,
                message: '智能情感记忆系统插件初始化成功',
                version: '2.0.0',
                capabilities: ['processConversation', 'generateContext', 'getMemoryStats', 'generateSystemPsychologyInstructions'] // 添加System心理指令生成
            };
            
        } catch (error) {
            this.logger.error('插件初始化', `初始化失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 处理对话 - 异步处理，不阻塞响应
     */
    async processConversation(params) {
        try {
            if (!this.isInitialized || !this.system) {
                this.logger.warning('对话处理', '插件未初始化，跳过记录');
                return { success: false, message: '插件未初始化' };
            }

            // 立即返回成功，异步处理记录
            setImmediate(() => {
                this.processConversationAsync(params);
            });

            return {
                success: true,
                message: '对话记录已提交异步处理',
                async: true
            };

        } catch (error) {
            this.logger.error('对话处理', `对话处理失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: '对话处理失败'
            };
        }
    }

    /**
     * 异步处理对话记录
     */
    async processConversationAsync(params) {
        const transactionId = this.generateTransactionId();
        
        try {
            this.logger.debug('异步处理', `开始处理对话事务 [${transactionId}]`, {
                userId: params.userId,
                messageLength: params.userMessage?.length || 0,
                responseLength: params.aiResponse?.length || 0
            });
            
            // 开始事务
            const transaction = await this.beginTransaction(transactionId);
            
            // 同步执行所有处理步骤
            const results = await this.executeAllProcessingSteps(params, transactionId);
            
            // 验证所有步骤是否成功
            if (this.validateTransactionResults(results)) {
                // 提交事务
                await this.commitTransaction(transactionId);
                
                this.logger.success('异步处理', `对话处理事务成功提交 [${transactionId}]`, {
                    processedSteps: Object.keys(results),
                    totalSteps: Object.keys(results).length
                });
                
            } else {
                throw new Error('部分处理步骤失败，需要回滚');
            }
            
        } catch (error) {
            this.logger.error('异步处理', `对话处理事务失败 [${transactionId}]: ${error.message}`);
            
            // 回滚事务
            await this.rollbackTransaction(transactionId);
        }
    }

    /**
     * 执行所有处理步骤
     */
    async executeAllProcessingSteps(params, transactionId) {
        const results = {};
        
        try {
            // 参数验证和清理
            if (!params.userId || typeof params.userId !== 'string') {
                throw new Error('userId参数无效或为空');
            }
            
            if (!params.userMessage || typeof params.userMessage !== 'string') {
                throw new Error('userMessage参数无效或为空');
            }
            
            if (!params.aiResponse || typeof params.aiResponse !== 'string') {
                throw new Error('aiResponse参数无效或为空');
            }
            
            // 确保personaName不为空
            const personaName = params.personaName || params.assistantName || 'Assistant';

            // 使用北京时间
            const beijingTime = new Date(new Date().getTime() + 8 * 60 * 60 * 1000).toISOString().replace('T', ' ').substring(0, 19);

            this.logger.debug('处理步骤', `开始执行所有处理步骤 [${transactionId}]`, {
                userId: params.userId,
                personaName: personaName,
                userMessageLength: params.userMessage.length,
                aiResponseLength: params.aiResponse.length,
                beijingTime: beijingTime
            });
            
            // 1. 综合分析 - 一次性完成情感、好感度、概念分析（优化API调用）
            this.logger.debug('处理步骤', '步骤1: 执行综合对话分析（优化版）');
            try {
                const comprehensiveAnalysis = await this.system.openaiService.comprehensivePostConversationAnalysis(
                    params.userMessage,
                    params.aiResponse,
                    params.userId,
                    personaName
                );

                // 解析综合分析结果（修复：适配实际返回的数据结构）
                results.emotionAnalysis = {
                    primary_emotion: comprehensiveAnalysis.user_emotion?.primary_emotion || 'neutral',
                    intensity: comprehensiveAnalysis.user_emotion?.intensity || 0.5,
                    valence: comprehensiveAnalysis.user_emotion?.valence || 0,
                    arousal: comprehensiveAnalysis.user_emotion?.arousal || 0.5,
                    confidence: 0.85,
                    analysis_method: 'comprehensive_openai_tools'
                };

                results.aiEmotionAnalysis = {
                    mood_state: comprehensiveAnalysis.ai_emotion?.mood_state || 'neutral',
                    mood_score: comprehensiveAnalysis.ai_emotion?.mood_score || 3,
                    response_tone: comprehensiveAnalysis.ai_emotion?.response_tone || 'neutral',
                    analysis_method: 'comprehensive_openai_tools'
                };

                results.affinityAnalysis = {
                    change_direction: comprehensiveAnalysis.affinity_impact?.change_direction || 'neutral',
                    change_magnitude: comprehensiveAnalysis.affinity_impact?.change_magnitude || 1,
                    reason: comprehensiveAnalysis.affinity_impact?.reason || '无明显变化',
                    analysis_method: 'comprehensive_openai_tools'
                };

                results.conceptLearning = {
                    concepts: comprehensiveAnalysis.concepts || [],
                    extracted_count: comprehensiveAnalysis.concepts?.length || 0,
                    analysis_method: 'comprehensive_openai_tools'
                };

                // 新增：回复质量分析结果
                results.responseQuality = {
                    relevance_score: comprehensiveAnalysis.response_quality?.relevance_score || 0.8,
                    completeness_score: comprehensiveAnalysis.response_quality?.completeness_score || 0.8,
                    helpfulness_score: comprehensiveAnalysis.response_quality?.helpfulness_score || 0.8,
                    clarity_score: comprehensiveAnalysis.response_quality?.clarity_score || 0.8,
                    overall_quality: comprehensiveAnalysis.response_quality?.overall_quality || 0.8,
                    quality_feedback: comprehensiveAnalysis.response_quality?.quality_feedback || '',
                    analysis_method: 'comprehensive_openai_tools'
                };

                // 新增：用户状态分析结果
                results.userAnalysis = {
                    positivity_score: comprehensiveAnalysis.user_analysis?.positivity_score || 0,
                    engagement_level: comprehensiveAnalysis.user_analysis?.engagement_level || 0.5,
                    satisfaction_score: comprehensiveAnalysis.user_analysis?.satisfaction_score || 0,
                    satisfaction_level: comprehensiveAnalysis.user_analysis?.satisfaction_level || 'neutral',
                    feedback_quality: comprehensiveAnalysis.user_analysis?.feedback_quality || 0.5,
                    analysis_method: 'comprehensive_openai_tools'
                };

                // 新增：交互质量分析结果
                results.interactionAnalysis = {
                    conversation_flow: comprehensiveAnalysis.interaction_analysis?.conversation_flow || 'normal',
                    topic_coherence: comprehensiveAnalysis.interaction_analysis?.topic_coherence || 0.7,
                    mutual_understanding: comprehensiveAnalysis.interaction_analysis?.mutual_understanding || 0.7,
                    communication_effectiveness: comprehensiveAnalysis.interaction_analysis?.communication_effectiveness || 0.7,
                    analysis_method: 'comprehensive_openai_tools'
                };

                // 保存概念到数据库
                if (comprehensiveAnalysis.concepts && comprehensiveAnalysis.concepts.length > 0) {
                    try {
                        await this.system.conceptLearner.updateConceptNeurons(
                            comprehensiveAnalysis.concepts,
                            results.aiEmotionAnalysis
                        );
                        this.logger.info('概念保存', `成功保存 ${comprehensiveAnalysis.concepts.length} 个概念到数据库`);
                    } catch (conceptSaveError) {
                        this.logger.error('概念保存', `保存概念失败: ${conceptSaveError.message}`);
                    }
                }

                this.logger.info('处理步骤', `综合分析完成: 情感=${results.emotionAnalysis.primary_emotion}, AI心情=${results.aiEmotionAnalysis.mood_state}, 概念数=${results.conceptLearning.extracted_count}, 质量=${results.responseQuality.overall_quality.toFixed(2)}, 满意度=${results.userAnalysis.satisfaction_level}`);

                // 将综合分析结果传递给后续步骤
                params.comprehensiveAnalysis = comprehensiveAnalysis;

                // 概念已经通过ConceptLearningService保存到concept_neurons表
                // 删除重复的概念保存逻辑，避免双重保存到不同表

            } catch (comprehensiveError) {
                this.logger.warning('处理步骤', `综合分析失败，回退到传统方法: ${comprehensiveError.message}`);

                // 回退到传统的分步分析
                results.emotionAnalysis = await this.system.emotionAnalyzer.analyzeMessage(
                    params.aiResponse,
                    {
                        messageType: 'assistant',
                        userMessage: params.userMessage,
                        timestamp: params.timestamp,
                        metadata: params.metadata
                    }
                );

                results.affinityAnalysis = {
                    affinity_delta: 0,
                    satisfaction_level: 0.5,
                    relationship_type: 'neutral',
                    interaction_quality: 'neutral',
                    trust_level: 0.5,
                    emotional_resonance: 0.5,
                    disabled: true,
                    message: '好感度分析已禁用'
                };

                // 优化：回退时也使用OpenAI Tools进行概念学习，避免重复
                try {
                    const fallbackConcepts = await this.system.conceptLearner.extractConceptsWithTools(
                        params.userMessage + ' ' + params.aiResponse,
                        params.userId,
                        personaName
                    );

                    results.conceptLearning = {
                        concepts: fallbackConcepts || [],
                        extracted_count: fallbackConcepts?.length || 0,
                        analysis_method: 'fallback_openai_tools'
                    };

                    // 保存概念到数据库
                    if (fallbackConcepts && fallbackConcepts.length > 0) {
                        try {
                            // 为回退概念生成含义描述
                            const conceptsWithMeaning = await this.system.conceptLearner.generateConceptMeanings(
                                fallbackConcepts,
                                params.userMessage + ' ' + params.aiResponse
                            );

                            await this.system.conceptLearner.updateConceptNeurons(
                                conceptsWithMeaning,
                                results.aiEmotionAnalysis
                            );
                            this.logger.info('概念保存', `回退模式成功保存 ${conceptsWithMeaning.length} 个概念到数据库`);
                        } catch (conceptSaveError) {
                            this.logger.error('概念保存', `回退模式保存概念失败: ${conceptSaveError.message}`);
                        }
                    }
                } catch (fallbackConceptError) {
                    this.logger.error('处理步骤', `回退概念学习也失败: ${fallbackConceptError.message}`);
                    results.conceptLearning = {
                        concepts: [],
                        extracted_count: 0,
                        analysis_method: 'failed'
                    };
                }
            }
            
            // 4. 对话历史记录（优化格式）
            this.logger.debug('处理步骤', '步骤4: 记录对话历史');
            results.conversationRecord = await this.system.conversationManager.addConversation(
                params.userId,
                params.userMessage,
                params.aiResponse,
                beijingTime, // 使用北京时间
                {
                    personaName: personaName, // 使用验证后的personaName
                    emotionData: results.emotionAnalysis,
                    affinityData: results.affinityAnalysis,
                    conceptData: results.conceptLearning,
                    metadata: {
                        ...params.metadata,
                        beijingTime: beijingTime,
                        transactionId: transactionId,
                        processingVersion: '2.0.0'
                    }
                }
            );

            // 5. 记忆嵌入生成和存储（优化格式）
            this.logger.debug('处理步骤', '步骤5: 生成和存储记忆嵌入');
            results.memoryEmbedding = await this.system.embeddingService.storeConversationEmbedding(
                params.userId,
                params.userMessage,
                params.aiResponse,
                {
                    personaName: personaName,
                    emotionData: results.emotionAnalysis,
                    timestamp: beijingTime, // 使用北京时间
                    conversationId: results.conversationRecord.id,
                    relatedConcepts: results.conceptLearning?.concepts || [],
                    metadata: {
                        transactionId: transactionId,
                        processingVersion: '2.0.0',
                        beijingTime: beijingTime
                    }
                }
            );

            // 5.5. 保存AI反馈结果到用户记忆文件
            this.logger.debug('处理步骤', '步骤5.5: 保存AI反馈结果到用户记忆文件');
            try {
                results.fileMemorySave = await this.system.embeddingService.saveRecentMemoryToFile(
                    params.userId,
                    personaName,
                    params.userMessage,
                    params.aiResponse,
                    results.emotionAnalysis
                );
                this.logger.info('处理步骤', `AI反馈结果已保存到用户 ${params.userId} 的记忆文件`);
            } catch (fileError) {
                this.logger.error('处理步骤', `保存AI反馈结果到文件失败: ${fileError.message}`);
                // 不影响主流程，继续执行
                results.fileMemorySave = false;
            }
            
            // 6. 用户好感度更新已禁用，保留对话处理逻辑
            this.logger.debug('处理步骤', '步骤6: 跳过用户好感度更新（已禁用）');

            // 提供默认的好感度更新结果以保持兼容性
            results.affinityUpdate = {
                success: true,
                message: '好感度更新已禁用',
                disabled: true,
                affinity_delta: 0,
                new_affinity: 0.5,
                relationship_type: 'neutral'
            };
            
            this.logger.info('处理步骤', `所有处理步骤执行完成 [${transactionId}]`, {
                completedSteps: Object.keys(results).length,
                stepsSummary: Object.keys(results)
            });
            
            return results;
            
        } catch (error) {
            this.logger.error('处理步骤', `处理步骤执行失败 [${transactionId}]: ${error.message}`);
            throw error;
        }
    }

    /**
     * 生成智能上下文
     */
    async generateContext(params) {
        try {
            if (!this.isInitialized || !this.system) {
                return {
                    success: false,
                    message: '插件未初始化',
                    context: null
                };
            }

            this.logger.debug('上下文生成', '开始生成智能上下文', {
                userId: params.userId,
                currentMessage: params.currentMessage,
                maxContextSize: params.maxContextSize,
                personaName: params.personaName || 'Assistant'
            });

            // 严格按照maxContextSize分配上下文项目
            const maxSize = params.maxContextSize; // 不提供默认值，严格按用户输入
            if (!maxSize || maxSize <= 0) {
                throw new Error('maxContextSize必须是大于0的数字');
            }

            // 重新设计分配算法 - 移除最近记忆，因为现在有更好的对话历史系统
            // 分配策略：情感(1) + 概念(25%) + 语义记忆(75%)
            // 注意：对话历史现在通过新的role-based系统处理，不在system消息中
            const emotionAllocation = 1; // 固定1个情感状态
            const conceptAllocation = Math.max(1, Math.floor(maxSize * 0.25)); // 25%给概念
            const semanticMemoryLimit = Math.max(1, maxSize - emotionAllocation - conceptAllocation); // 剩余全部给语义记忆

            this.logger.info('上下文生成', `分配策略 [总计${maxSize}]: 情感=${emotionAllocation}, 概念=${conceptAllocation}, 语义=${semanticMemoryLimit} (对话历史通过role-based系统处理)`);

            // 获取相关记忆
            let relevantMemories = [];

            try {
                if (this.system.embeddingService && this.system.embeddingService.isInitialized) {
                    this.logger.debug('上下文生成', `语义记忆分配: ${semanticMemoryLimit}/${maxSize}`);

                    relevantMemories = await this.system.embeddingService.findRelevantMemories(
                        params.currentMessage,
                        {
                            userId: params.userId,
                            personaName: params.personaName || params.assistantName || 'Assistant',
                            limit: semanticMemoryLimit,
                            threshold: 0.25  // 降低阈值以获取更多相关记忆
                        }
                    );

                    // 确保relevantMemories是数组
                    if (!Array.isArray(relevantMemories)) {
                        this.logger.warning('上下文生成', '相关记忆查询返回非数组，使用空数组');
                        relevantMemories = [];
                    }
                } else {
                    this.logger.warning('上下文生成', '嵌入服务未初始化，使用空上下文');
                }
            } catch (memoryError) {
                this.logger.error('上下文生成', `获取相关记忆失败: ${memoryError.message}`);
                relevantMemories = [];
            }

            // 注意：对话历史现在通过新的role-based系统处理，不在这里获取
            this.logger.debug('上下文生成', '对话历史通过role-based系统处理，跳过旧的对话历史获取');

            // 获取相关概念信息（基于向量嵌入）
            let relevantConcepts = [];

            try {
                if (this.system.conceptLearner && params.userId) {
                    this.logger.debug('上下文生成', `概念分配: ${conceptAllocation}/${maxSize}`);

                    relevantConcepts = await this.system.conceptLearner.getRelevantConcepts(
                        params.currentMessage || params.userMessage || '',
                        params.userId,
                        params.personaName || params.assistantName || 'Assistant',
                        conceptAllocation
                    );

                    this.logger.info('上下文生成', `获取到 ${relevantConcepts.length} 个相关概念 (请求${conceptAllocation}个)`);
                }
            } catch (conceptError) {
                this.logger.error('上下文生成', `获取相关概念失败: ${conceptError.message}`);
            }

            // 获取已保存的AI情绪状态、用户好感度和高级心理状态（从历史数据中读取）
            let aiEmotionState = {};
            let userAffinityState = {};
            let aiStressState = {};
            let memeState = {};
            let worldTreeState = {};

            try {
                // 1. 获取AI的情绪状态（从数据库读取最新状态）
                aiEmotionState = await this.getAIEmotionState(params.userId, params.personaName || 'Assistant');

                // 2. 获取用户好感度状态（从数据库读取最新状态）
                userAffinityState = await this.getUserAffinityState(params.userId, params.personaName || 'Assistant');

                // 3. 获取AI压力值状态（从数据库读取最新状态）
                aiStressState = await this.getAIStressState(params.userId, params.personaName || 'Assistant');

                // 4. 获取模因认知状态（从数据库读取最新状态）
                memeState = await this.getMemeState(params.userId, params.personaName || 'Assistant');

                // 5. 获取世界树背景状态（从数据库读取最新状态）
                worldTreeState = await this.getWorldTreeState(params.userId, params.personaName || 'Assistant');

                this.logger.info('上下文生成', `获取到AI状态 - 情绪: ${aiEmotionState.current_emotion || '中性'}(${aiEmotionState.emotion_value || 0}), 好感度: ${userAffinityState.affinity_value || 0}, 压力值: ${aiStressState.stress_value || 0}(${aiStressState.stress_level || 'normal'}), 模因: ${memeState.evolution_stage || '初始'}, 世界树: ${worldTreeState.current_branch || '未知'}`);
            } catch (error) {
                this.logger.error('上下文生成', `获取AI状态失败: ${error.message}`);
                aiEmotionState = { current_emotion: '中性', emotion_value: 0 };
                userAffinityState = { affinity_value: 0 };
                aiStressState = { stress_value: 0.0, stress_level: 'normal' };
                memeState = { evolution_stage: '初始', memetic_influence: 0.0 };
                worldTreeState = { current_branch: '未知', character_role: '观察者' };
            }

            // 构建分板块格式化上下文 - 严格按照maxContextSize控制总数量
            const contextItems = [];

            // 【AI状态板块】- AI情绪状态、用户好感度和高级心理状态
            const aiStateData = this.formatAIStateContext(aiEmotionState, userAffinityState, aiStressState, memeState, worldTreeState, params.userId, params.personaName);
            if (aiStateData) {
                contextItems.push({
                    type: 'ai_state_section',
                    section_title: '【AI状态】',
                    content: aiStateData,
                    priority: 1,
                    metadata: {
                        emotion_value: aiEmotionState.emotion_value || 0,
                        affinity_value: userAffinityState.affinity_value || 0,
                        emotion_trend: aiEmotionState.trend,
                        affinity_trend: userAffinityState.trend
                    }
                });
            }

            // 【概念学习板块】- 相关概念及其含义
            if (relevantConcepts && relevantConcepts.length > 0) {
                const conceptsData = this.formatActiveConceptsContext(relevantConcepts, conceptAllocation);
                if (conceptsData) {
                    contextItems.push({
                        type: 'concepts_section',
                        section_title: '【相关概念】',
                        content: conceptsData,
                        priority: 2,
                        metadata: {
                            total_concepts: relevantConcepts.length,
                            displayed_concepts: Math.min(relevantConcepts.length, conceptAllocation),
                            avg_relevance: relevantConcepts.length > 0 ?
                                (relevantConcepts.reduce((sum, c) => sum + (c.relevance_score || 0), 0) / relevantConcepts.length).toFixed(3) : 0
                        }
                    });
                }
            }

            // 【语义相关记忆板块】- 相似度、时间、重要性
            if (relevantMemories && relevantMemories.length > 0) {
                const semanticData = this.formatSemanticMemoriesContext(relevantMemories, semanticMemoryLimit, params.userId, params.personaName);
                if (semanticData) {
                    contextItems.push({
                        type: 'semantic_memories_section',
                        section_title: '【语义相关记忆】',
                        content: semanticData,
                        priority: 3,
                        metadata: {
                            total_memories: relevantMemories.length,
                            displayed_memories: Math.min(relevantMemories.length, semanticMemoryLimit),
                            avg_similarity: relevantMemories.length > 0 ?
                                (relevantMemories.reduce((sum, m) => sum + (m.similarity || 0), 0) / relevantMemories.length).toFixed(3) : 0
                        }
                    });
                }
            }

            // 注意：最近记忆和对话历史现在通过新的role-based系统处理，不在system消息中显示
            this.logger.debug('上下文生成', '跳过最近记忆和对话历史板块，这些现在通过role-based系统处理');

            // 按优先级排序上下文板块
            contextItems.sort((a, b) => (a.priority || 0) - (b.priority || 0));

            // 构建最终的格式化上下文字符串
            let formattedContext = '';
            let totalSections = 0;

            contextItems.forEach((section, index) => {
                if (section.content && section.section_title) {
                    formattedContext += `${section.section_title}\n`;
                    formattedContext += `  ${section.content}\n\n`;
                    totalSections++;
                }
            });

            // 验证总数量控制
            if (totalSections > maxSize) {
                this.logger.warning('上下文生成', `上下文板块数量 ${totalSections} 超过限制 ${maxSize}，已按优先级排序`);
                // 截断超出的板块
                const limitedSections = contextItems.slice(0, maxSize);
                formattedContext = '';
                limitedSections.forEach(section => {
                    if (section.content && section.section_title) {
                        formattedContext += `${section.section_title}\n`;
                        formattedContext += `  ${section.content}\n\n`;
                    }
                });
                totalSections = limitedSections.length;
            }

            this.logger.success('上下文生成', `分板块格式化上下文生成成功`, {
                userId: params.userId,
                personaName: params.personaName,
                totalSections: totalSections,
                maxContextSize: maxSize,
                contextLength: formattedContext.length
            });

            return {
                success: true,
                context: formattedContext,
                metadata: {
                    memoryCount: relevantMemories.length,
                    conceptsCount: relevantConcepts.length,
                    totalSections: totalSections,
                    maxContextSize: maxSize,
                    formattedLength: formattedContext.length
                }
            };

        } catch (error) {
            this.logger.error('上下文生成', `智能上下文生成失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                context: []
            };
        }
    }

    /**
     * 获取用户好感度（已禁用）
     */
    async getUserAffinity(params) {
        try {
            if (!this.isInitialized) {
                throw new Error('插件未初始化');
            }

            this.logger.info('好感度查询', '好感度管理器已禁用，返回默认值', {
                userId: params.userId
            });

            return {
                success: true,
                data: {
                    currentAffinity: 0.5,
                    relationshipType: 'neutral',
                    disabled: true,
                    message: '好感度管理器已禁用'
                },
                message: '好感度管理器已禁用，返回默认值'
            };

        } catch (error) {
            this.logger.error('好感度查询', `用户好感度查询失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: '用户好感度查询失败'
            };
        }
    }

    /**
     * 获取指定Agent的所有用户列表
     */
    async getUsersByAgent(agentName) {
        try {
            if (!this.isInitialized) {
                throw new Error('插件未初始化');
            }

            this.logger.info('用户查询', `获取Agent ${agentName} 的用户列表`);

            // 从多个表中获取用户数据，按persona_name（即agentName）分组
            const users = await this.system.dbAll(`
                SELECT
                    u.user_id,
                    u.persona_name,
                    u.current_affinity,
                    u.relationship_type,
                    u.total_interactions,
                    u.last_interaction,
                    COALESCE(e.emotion_value, 0) as current_emotion,
                    COALESCE(s.stress_value, 0) as current_stress,
                    COUNT(DISTINCT c.id) as conversation_count
                FROM user_affinity u
                LEFT JOIN user_emotion_states e ON u.user_id = e.user_id AND u.persona_name = e.persona_name
                LEFT JOIN user_stress_states s ON u.user_id = s.user_id AND u.persona_name = s.persona_name
                LEFT JOIN conversation_history c ON u.user_id = c.user_id AND u.persona_name = c.persona_name
                WHERE u.persona_name = ?
                GROUP BY u.user_id, u.persona_name
                ORDER BY u.last_interaction DESC
            `, [agentName]);

            this.logger.info('用户查询', `Agent ${agentName} 找到 ${users.length} 个用户`);

            return users || [];

        } catch (error) {
            this.logger.error('用户查询', `获取Agent用户列表失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 生成智能上下文（向后兼容接口）
     */
    async generateIntelligentContext(params) {
        try {
            this.logger.debug('智能上下文生成', '收到调用请求', {
                paramsType: typeof params,
                paramsKeys: params ? Object.keys(params) : 'params为null'
            });

            if (!this.isInitialized || !this.system) {
                this.logger.warning('智能上下文生成', '插件未初始化，返回空上下文');
                return {
                    success: false,
                    context: '',
                    error: '智能情感记忆系统插件未初始化',
                    userId: params?.userId || 'unknown',
                    personaName: params?.personaName || 'Assistant'
                };
            }

            if (!params || typeof params !== 'object') {
                this.logger.error('智能上下文生成', '参数格式错误，期望对象');
                return {
                    success: false,
                    context: '',
                    error: '参数格式错误，期望对象',
                    userId: 'unknown',
                    personaName: 'Assistant'
                };
            }

            // 参数解构和验证
            const {
                userId,
                userMessage,
                currentMessage, // 兼容不同的参数名
                personaName = 'Assistant',
                maxContextSize = 10
            } = params;

            // 优先使用userMessage，fallback到currentMessage
            const messageContent = userMessage || currentMessage;
            
            this.logger.debug('智能上下文生成', '参数解构结果', {
                userId: userId,
                userIdType: typeof userId,
                messageContent: messageContent?.substring(0, 50) + '...',
                messageType: typeof messageContent,
                personaName: personaName,
                maxContextSize: maxContextSize
            });
            
            if (!userId || typeof userId !== 'string') {
                this.logger.error('智能上下文生成', `userId参数无效: ${userId} (${typeof userId})`);
                return {
                    success: false,
                    context: '',
                    error: `userId参数无效: ${userId} (${typeof userId})`,
                    userId: userId || 'unknown',
                    personaName: params.personaName || 'Assistant'
                };
            }
            
            if (!messageContent || typeof messageContent !== 'string') {
                this.logger.error('智能上下文生成', `messageContent参数无效: ${messageContent} (${typeof messageContent})`);
                return {
                    success: false,
                    context: '',
                    error: `messageContent参数无效: ${messageContent} (${typeof messageContent})`,
                    userId: userId,
                    personaName: params.personaName || 'Assistant'
                };
            }

            // 调用底层系统的generateIntelligentContext方法
            const options = {
                maxContextSize: maxContextSize,
                personaName: personaName
            };
            
            this.logger.debug('智能上下文生成', `调用底层系统`, {
                userId: userId,
                messageLength: messageContent.length,
                personaName: personaName,
                options: options
            });
            
            const contextResult = await this.system.generateIntelligentContext(userId, messageContent, options);
            
            this.logger.success('智能上下文生成', `智能上下文生成成功 [${userId}]`, {
                contextLength: contextResult?.length || 0
            });

            // 返回components/memorySystem.js期望的对象格式
            return {
                success: true,
                context: contextResult || '',
                contextLength: contextResult?.length || 0,
                userId: userId,
                personaName: params.personaName
            };

        } catch (error) {
            this.logger.error('智能上下文生成', `智能上下文生成失败: ${error.message}`);
            this.logger.error('智能上下文生成', `错误堆栈: ${error.stack}`);
            
            // 返回components/memorySystem.js期望的错误对象格式
            return {
                success: false,
                context: '',
                error: error.message,
                userId: params?.userId || 'unknown',
                personaName: params?.personaName || 'Assistant'
            };
        }
    }

    /**
     * 获取记忆系统统计信息
     */
    async getMemoryStats() {
        try {
            if (!this.isInitialized) {
                throw new Error('插件未初始化');
            }
            
            const stats = await this.system.getSystemStats();
            
            this.logger.info('系统统计', '记忆系统统计信息获取成功', {
                totalMemories: stats.totalMemories,
                activeUsers: stats.activeUsers,
                totalConcepts: stats.totalConcepts
            });
            
            return {
                success: true,
                stats,
                message: '记忆系统统计信息获取成功'
            };
            
        } catch (error) {
            this.logger.error('系统统计', `记忆系统统计信息获取失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                message: '记忆系统统计信息获取失败'
            };
        }
    }

    /**
     * 事务管理相关方法
     */
    generateTransactionId() {
        return `txn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    async beginTransaction(transactionId) {
        const transaction = {
            id: transactionId,
            startTime: new Date(),
            status: 'active',
            rollbackData: []
        };
        
        this.transactionMap.set(transactionId, transaction);
        this.logger.debug('事务管理', `开始事务: ${transactionId}`);
        
        return transaction;
    }

    validateTransactionResults(results) {
        // 移除好感度相关的验证步骤，保留核心功能
        const requiredSteps = ['emotionAnalysis', 'conceptLearning', 'conversationRecord', 'memoryEmbedding'];

        for (const step of requiredSteps) {
            if (!results[step] || (results[step].success === false)) {
                this.logger.warning('事务验证', `步骤失败: ${step}`);
                return false;
            }
        }

        // 好感度相关步骤为可选，不影响事务成功
        if (results.affinityAnalysis && results.affinityAnalysis.disabled) {
            this.logger.debug('事务验证', '好感度分析已禁用，跳过验证');
        }
        if (results.affinityUpdate && results.affinityUpdate.disabled) {
            this.logger.debug('事务验证', '好感度更新已禁用，跳过验证');
        }

        return true;
    }

    async commitTransaction(transactionId) {
        const transaction = this.transactionMap.get(transactionId);
        if (transaction) {
            transaction.status = 'committed';
            transaction.endTime = new Date();
            this.logger.debug('事务管理', `提交事务: ${transactionId}`);
        }
    }

    async rollbackTransaction(transactionId) {
        const transaction = this.transactionMap.get(transactionId);
        if (transaction) {
            transaction.status = 'rolled_back';
            transaction.endTime = new Date();
            
            // 这里可以实现具体的回滚逻辑
            this.logger.warning('事务管理', `回滚事务: ${transactionId}`);
        }
    }

    /**
     * 加载配置
     */
    loadConfig(inputConfig) {
        try {
            // 1. 首先加载插件自己的config.env文件
            const pluginConfig = this.loadPluginEnvConfig();

            // 2. 默认配置
            const defaultConfig = {
                EMOTION_MEMORY_ENABLED: true,
                AFFINITY_TRACKING_ENABLED: false, // 好感度跟踪已禁用
                CONCEPT_LEARNING_ENABLED: true,
                BACKGROUND_THREADS_ENABLED: true,
                ENABLE_DETAILED_LOGGING: true,
                LOG_LEVEL: 'info',
                TRANSACTION_TIMEOUT: 30,
                ENABLE_ROLLBACK_ON_ERROR: true
            };

            // 3. 配置优先级：插件配置 > 主服务器配置 > 默认配置
            // 只有当插件配置中没有某个配置项时，才使用主服务器的配置
            const config = { ...defaultConfig };

            // 先应用主服务器配置（作为备用）
            Object.keys(inputConfig || {}).forEach(key => {
                if (inputConfig[key] !== undefined && inputConfig[key] !== '') {
                    config[key] = inputConfig[key];
                }
            });

            // 最后应用插件配置（优先级最高）
            Object.keys(pluginConfig).forEach(key => {
                if (pluginConfig[key] !== undefined && pluginConfig[key] !== '') {
                    config[key] = pluginConfig[key];
                }
            });

            this.logger.info('配置加载', `插件配置加载完成，配置项数量: ${Object.keys(config).length}`);
            this.logger.info('配置加载', `OpenAI API URL: ${config.OPENAI_API_URL || '未配置'} (来源: ${pluginConfig.OPENAI_API_URL ? '插件配置' : '主服务器配置'})`);
            this.logger.info('配置加载', `OpenAI 模型: ${config.OPENAI_MODEL || '未配置'} (来源: ${pluginConfig.OPENAI_MODEL ? '插件配置' : '主服务器配置'})`);
            this.logger.info('配置加载', `嵌入模型: ${config.OPENAI_EMBEDDING_MODEL || '未配置'} (来源: ${pluginConfig.OPENAI_EMBEDDING_MODEL ? '插件配置' : '主服务器配置'})`);

            return config;

        } catch (error) {
            this.logger.error('配置加载', `配置加载失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 加载插件自己的config.env配置文件
     */
    loadPluginEnvConfig() {
        try {
            const configPath = path.join(__dirname, 'config.env');

            if (!fs.existsSync(configPath)) {
                this.logger.warning('配置加载', 'config.env文件不存在，将使用主服务器配置作为备用');
                return {};
            }

            const configContent = fs.readFileSync(configPath, 'utf8');
            const config = {};

            // 解析.env格式的配置文件
            configContent.split('\n').forEach(line => {
                line = line.trim();

                // 跳过注释和空行
                if (!line || line.startsWith('#')) {
                    return;
                }

                // 解析键值对
                const equalIndex = line.indexOf('=');
                if (equalIndex > 0) {
                    const key = line.substring(0, equalIndex).trim();
                    const value = line.substring(equalIndex + 1).trim();

                    // 处理布尔值
                    if (value.toLowerCase() === 'true') {
                        config[key] = true;
                    } else if (value.toLowerCase() === 'false') {
                        config[key] = false;
                    } else if (!isNaN(value) && value !== '') {
                        // 处理数字
                        config[key] = parseFloat(value);
                    } else {
                        // 字符串值
                        config[key] = value;
                    }
                }
            });

            this.logger.info('配置加载', `从插件config.env加载了 ${Object.keys(config).length} 个配置项`);

            return config;

        } catch (error) {
            this.logger.error('配置加载', '加载插件config.env失败:', error.message);
            return {};
        }
    }



    /**
     * 写入日志文件
     */
    writeLog(logEntry) {
        if (!this.config?.ENABLE_DETAILED_LOGGING) return;
        
        try {
            const logsDir = path.join(__dirname, 'logs');
            if (!fs.existsSync(logsDir)) {
                fs.mkdirSync(logsDir, { recursive: true });
            }
            
            const logFile = path.join(logsDir, `emotion_memory_${new Date().toISOString().split('T')[0]}.log`);
            const logLine = JSON.stringify(logEntry) + '\n';
            
            fs.appendFileSync(logFile, logLine);
        } catch (error) {
            this.logger.error('日志写入', `写入日志失败: ${error.message}`);
        }
    }

    /**
     * 插件清理
     */
    async cleanup() {
        try {
            if (this.system) {
                await this.system.shutdown();
            }
            
            this.isInitialized = false;
            this.logger.info('插件清理', '智能情感记忆系统插件清理完成');
            
        } catch (error) {
            this.logger.error('插件清理', `插件清理失败: ${error.message}`);
        }
    }

    /**
     * 简化的好感度变化计算
     */
    calculateSimpleAffinityDelta(emotionState, params) {
        try {
            let delta = 0;
            
            // 基于情感效价和强度
            if (emotionState.valence && emotionState.intensity) {
                delta += emotionState.valence * emotionState.intensity * 0.15;
            }
            
            // 基于消息长度（更长的消息通常表示更多投入）
            if (params.userMessage) {
                const messageLength = params.userMessage.length;
                if (messageLength > 100) delta += 0.02;
                if (messageLength > 500) delta += 0.03;
            }
            
            // 基于AI回复质量（简单估算）
            if (params.aiResponse) {
                const responseLength = params.aiResponse.length;
                if (responseLength > 50) delta += 0.01;
                if (responseLength > 200) delta += 0.02;
            }
            
            // 随机波动模拟真实关系
            delta += (Math.random() - 0.5) * 0.02;
            
            // 限制变化范围
            return Math.max(-0.2, Math.min(0.2, delta));
            
        } catch (error) {
            this.logger.error('好感度计算', `计算好感度变化失败: ${error.message}`);
            return 0.01; // 默认小幅正面变化
        }
    }

    /**
     * 简化的关系类型判断
     */
    determineRelationshipType(affinityDelta) {
        if (affinityDelta > 0.1) return 'friend';
        if (affinityDelta < -0.1) return 'stranger';
        return 'acquaintance';
    }

    /**
     * 生成情感状态描述
     */
    generateEmotionDescription(userState) {
        try {
            if (!userState || typeof userState !== 'object') {
                return null;
            }

            const parts = [];

            // 好感度信息
            if (userState.current_affinity !== undefined) {
                const affinityLevel = userState.current_affinity > 0.7 ? '很高' :
                                    userState.current_affinity > 0.3 ? '较高' :
                                    userState.current_affinity > -0.3 ? '中等' :
                                    userState.current_affinity > -0.7 ? '较低' : '很低';
                parts.push(`好感度${affinityLevel}(${userState.current_affinity.toFixed(2)})`);
            }

            // 关系类型
            if (userState.relationship_type) {
                const relationshipMap = {
                    'stranger': '陌生人',
                    'acquaintance': '熟人',
                    'friend': '朋友',
                    'close_friend': '密友',
                    'family': '家人'
                };
                parts.push(`关系: ${relationshipMap[userState.relationship_type] || userState.relationship_type}`);
            }

            // 情感状态
            if (userState.emotion_valence !== undefined) {
                const valenceDesc = userState.emotion_valence > 0.3 ? '积极' :
                                  userState.emotion_valence < -0.3 ? '消极' : '中性';
                parts.push(`情绪${valenceDesc}`);
            }

            // 互动统计
            if (userState.total_interactions) {
                parts.push(`共${userState.total_interactions}次互动`);
            }

            return parts.length > 0 ? `用户状态: ${parts.join(', ')}` : null;

        } catch (error) {
            this.logger.error('情感描述', `生成情感描述失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 格式化记忆内容为统一格式
     * 格式：YYYY年MM月DD日 HH:mm 北京时间，用户XX说XX，我回复XXX
     */
    formatMemoryContent(memory, userId, assistantName) {
        try {
            if (!memory || !memory.content) {
                return memory?.content || '';
            }

            // 解析时间戳为北京时间
            let beijingTime = '';
            if (memory.creation_time) {
                try {
                    // 如果已经是北京时间格式，直接使用
                    if (memory.creation_time.includes('北京时间')) {
                        beijingTime = memory.creation_time;
                    } else {
                        // 否则解析ISO时间戳
                        const date = new Date(memory.creation_time);
                        if (!isNaN(date.getTime())) {
                            // 获取北京时间并格式化
                            const beijingDate = new Date(date.toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
                            const year = beijingDate.getFullYear();
                            const month = String(beijingDate.getMonth() + 1).padStart(2, '0');
                            const day = String(beijingDate.getDate()).padStart(2, '0');
                            const hours = String(beijingDate.getHours()).padStart(2, '0');
                            const minutes = String(beijingDate.getMinutes()).padStart(2, '0');
                            beijingTime = `${year}年${month}月${day}日 ${hours}:${minutes} 北京时间`;
                        } else {
                            beijingTime = '时间未知';
                        }
                    }
                } catch (timeError) {
                    beijingTime = '时间未知';
                }
            } else {
                beijingTime = '时间未知';
            }

            // 尝试解析原始内容，提取用户消息和AI回复
            const content = memory.content;
            let userMessage = '';
            let aiResponse = '';

            // 匹配不同的内容格式
            const patterns = [
                // 格式1: "用户XX说XXX，我回复XXX" (统一格式)
                /用户[^说]*说(.+?)，我回复(.+)/s,
                // 格式2: "用户XX说现在发言的用户是XX，XXX，我回复XXX" (包含用户ID前缀)
                /用户[^说]*说现在发言的用户是[^，]*，(.+?)，我回复(.+)/s,
                // 格式3: "用户: xxx\nAI: xxx"
                /用户:\s*(.+?)\s*(?:AI|助手|我):\s*(.+)/s,
                // 格式4: "现在发言的用户是xxx，xxx\nAI: xxx"
                /现在发言的用户是[^，]*，(.+?)\s*(?:AI|助手|我):\s*(.+)/s,
                // 格式5: 直接的对话内容
                /(.+?)\s*(?:AI|助手|我):\s*(.+)/s
            ];

            let matched = false;
            for (const pattern of patterns) {
                const match = content.match(pattern);
                if (match) {
                    userMessage = match[1].trim();
                    aiResponse = match[2].trim();
                    matched = true;
                    break;
                }
            }

            // 如果无法解析，使用原始内容
            if (!matched) {
                return `${beijingTime}，记忆内容: ${content}`;
            }

            // 清理用户名
            const cleanUserId = userId || '用户';
            const cleanAssistantName = assistantName || '我';

            // 格式化为统一格式
            return `${beijingTime}，用户${cleanUserId}说"${userMessage}"，${cleanAssistantName}回复"${aiResponse}"`;

        } catch (error) {
            this.logger.error('上下文生成', `格式化记忆内容失败: ${error.message}`);
            return memory?.content || '';
        }
    }

    /**
     * 获取用户情感历史数据
     */
    async getEmotionHistory(userId, personaName, limit = 10) {
        try {
            if (!this.system.database) return [];

            const query = `
                SELECT emotion_state, creation_time, user_message, assistant_message
                FROM emotion_memories
                WHERE user_id = ? AND persona_name = ?
                AND emotion_state IS NOT NULL
                ORDER BY creation_time DESC
                LIMIT ?
            `;

            const rows = this.system.database.prepare(query).all(userId, personaName, limit);
            return rows.map(row => ({
                ...row,
                emotion_state: JSON.parse(row.emotion_state || '{}')
            }));
        } catch (error) {
            this.logger.error('情感历史', `获取情感历史失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 计算复杂情感状态（包含趋势、稳定性、强度变化）
     */
    async calculateComplexEmotionState(currentEmotion, emotionHistory, userId, personaName) {
        try {
            // 基础情感状态
            let complexState = { ...currentEmotion };

            if (emotionHistory.length > 0) {
                // 计算情感趋势
                const trend = this.calculateEmotionTrend(emotionHistory);
                complexState.trend = trend;

                // 计算情感稳定性
                const stability = this.calculateEmotionStability(emotionHistory);
                complexState.stability = stability;

                // 计算情感强度变化
                const intensityChange = this.calculateIntensityChange(currentEmotion, emotionHistory[0]?.emotion_state);
                complexState.intensity_change = intensityChange;

                // 计算情感持续时间
                const duration = this.calculateEmotionDuration(emotionHistory);
                complexState.duration = duration;

                // 预测情感发展
                const prediction = this.predictEmotionDevelopment(emotionHistory, currentEmotion);
                complexState.prediction = prediction;
            }

            // 保存复杂情感状态到数据库
            await this.saveComplexEmotionState(userId, personaName, complexState);

            return complexState;
        } catch (error) {
            this.logger.error('复杂情感分析', `计算复杂情感状态失败: ${error.message}`);
            return currentEmotion;
        }
    }

    /**
     * 计算情感趋势
     */
    calculateEmotionTrend(emotionHistory) {
        if (emotionHistory.length < 3) return '数据不足';

        const recentEmotions = emotionHistory.slice(0, 3);
        const valenceValues = recentEmotions.map(h => h.emotion_state?.valence || 0);

        const trend = valenceValues[0] - valenceValues[2];

        if (trend > 0.2) return '积极上升';
        if (trend < -0.2) return '消极下降';
        return '相对稳定';
    }

    /**
     * 计算情感稳定性
     */
    calculateEmotionStability(emotionHistory) {
        if (emotionHistory.length < 2) return '数据不足';

        const valenceValues = emotionHistory.map(h => h.emotion_state?.valence || 0);
        const variance = this.calculateVariance(valenceValues);

        if (variance < 0.1) return '非常稳定';
        if (variance < 0.3) return '较为稳定';
        if (variance < 0.5) return '轻微波动';
        return '情感波动较大';
    }

    /**
     * 计算方差
     */
    calculateVariance(values) {
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        const squaredDiffs = values.map(val => Math.pow(val - mean, 2));
        return squaredDiffs.reduce((sum, val) => sum + val, 0) / values.length;
    }

    /**
     * 计算强度变化
     */
    calculateIntensityChange(currentEmotion, lastEmotion) {
        if (!lastEmotion) return '首次记录';

        const currentIntensity = currentEmotion.intensity || 0;
        const lastIntensity = lastEmotion.intensity || 0;
        const change = currentIntensity - lastIntensity;

        if (change > 0.2) return '强度显著增强';
        if (change < -0.2) return '强度显著减弱';
        return '强度基本稳定';
    }

    /**
     * 计算情感持续时间
     */
    calculateEmotionDuration(emotionHistory) {
        if (emotionHistory.length < 2) return '无法计算';

        const latestTime = new Date(emotionHistory[0].creation_time);
        const earliestTime = new Date(emotionHistory[emotionHistory.length - 1].creation_time);
        const durationHours = (latestTime - earliestTime) / (1000 * 60 * 60);

        if (durationHours < 1) return '不足1小时';
        if (durationHours < 24) return `约${Math.round(durationHours)}小时`;
        return `约${Math.round(durationHours / 24)}天`;
    }

    /**
     * 预测情感发展
     */
    predictEmotionDevelopment(emotionHistory, currentEmotion) {
        if (emotionHistory.length < 3) return '数据不足以预测';

        const recentTrend = this.calculateEmotionTrend(emotionHistory);
        const stability = this.calculateEmotionStability(emotionHistory);

        if (recentTrend === '积极上升' && stability !== '情感波动较大') {
            return '情感可能继续向积极方向发展';
        } else if (recentTrend === '消极下降' && stability !== '情感波动较大') {
            return '需要关注，情感可能继续下降';
        } else if (stability === '情感波动较大') {
            return '情感状态不稳定，需要密切关注';
        }

        return '情感状态相对稳定';
    }

    /**
     * 保存复杂情感状态
     */
    async saveComplexEmotionState(userId, personaName, complexState) {
        try {
            if (!this.system.db) return;

            // 确保表存在
            await this.system.dbRun(`
                CREATE TABLE IF NOT EXISTS user_emotion_states (
                    user_id TEXT,
                    persona_name TEXT,
                    emotion_state TEXT,
                    trend TEXT,
                    stability TEXT,
                    prediction TEXT,
                    updated_time TEXT,
                    PRIMARY KEY (user_id, persona_name)
                )
            `);

            const query = `
                INSERT OR REPLACE INTO user_emotion_states
                (user_id, persona_name, emotion_state, trend, stability, prediction, updated_time)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `;

            await this.system.dbRun(query, [
                userId,
                personaName,
                JSON.stringify(complexState),
                complexState.trend || '',
                complexState.stability || '',
                complexState.prediction || '',
                new Date().toISOString()
            ]);
        } catch (error) {
            this.logger.error('情感状态保存', `保存复杂情感状态失败: ${error.message}`);
        }
    }

    /**
     * 获取详细的概念信息
     */
    async getDetailedConceptInfo(concept, currentMessage) {
        try {
            // 基础信息
            let detailedInfo = {
                detailedMeaning: concept.meaning || concept.description || '相关概念',
                type: concept.concept_type || '未知',
                usageCount: concept.usage_count || 0,
                lastUsed: concept.last_used || null
            };

            // 禁用上下文生成时的概念解释 - 这应该在对话后异步处理
            // 概念解释现在只在对话后的概念学习阶段执行

            // 添加使用统计信息
            if (detailedInfo.usageCount > 0) {
                detailedInfo.detailedMeaning += ` (使用${detailedInfo.usageCount}次)`;
            }

            // 添加类型信息
            const typeDescription = this.getConceptTypeDescription(detailedInfo.type);
            if (typeDescription) {
                detailedInfo.detailedMeaning += ` [${typeDescription}]`;
            }

            return detailedInfo;
        } catch (error) {
            this.logger.error('概念详情', `获取详细概念信息失败: ${error.message}`);
            return {
                detailedMeaning: concept.meaning || concept.description || '相关概念',
                type: concept.concept_type || '未知'
            };
        }
    }

    /**
     * 获取概念类型描述
     */
    getConceptTypeDescription(conceptType) {
        const typeMap = {
            'entity': '实体概念',
            'abstract': '抽象概念',
            'action': '动作概念',
            'emotion': '情感概念',
            'location': '地点概念',
            'time': '时间概念',
            'object': '物体概念',
            'person': '人物概念',
            'event': '事件概念',
            'skill': '技能概念',
            'knowledge': '知识概念'
        };

        return typeMap[conceptType] || '其他概念';
    }

    /**
     * 获取AI的情绪状态（从数据库读取最新状态）
     */
    async getAIEmotionState(userId, personaName) {
        try {
            if (!this.system.db) {
                return { current_emotion: '中性', emotion_value: 0 };
            }

            // 确保表存在（添加唯一约束）
            await this.system.dbRun(`
                CREATE TABLE IF NOT EXISTS ai_emotion_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    persona_name TEXT NOT NULL,
                    current_emotion TEXT NOT NULL,
                    emotion_value REAL NOT NULL,
                    emotion_type TEXT,
                    intensity REAL,
                    trigger_event TEXT,
                    trend TEXT,
                    stability REAL,
                    timestamp TEXT NOT NULL,
                    UNIQUE(user_id, persona_name)
                )
            `);

            const query = `
                SELECT * FROM ai_emotion_states
                WHERE user_id = ? AND persona_name = ?
                ORDER BY timestamp DESC
                LIMIT 1
            `;

            const result = await this.system.dbGet(query, [userId, personaName]);

            if (result) {
                return {
                    current_emotion: result.current_emotion,
                    emotion_value: result.emotion_value,
                    trend: result.trend,
                    stability: result.stability,
                    timestamp: result.timestamp
                };
            } else {
                return { current_emotion: '中性', emotion_value: 0 };
            }
        } catch (error) {
            this.logger.error('AI情绪状态', `获取AI情绪状态失败: ${error.message}`);
            return { current_emotion: '中性', emotion_value: 0 };
        }
    }

    /**
     * 获取用户好感度状态（从数据库读取最新状态）
     */
    async getUserAffinityState(userId, personaName) {
        try {
            if (!this.system.db) {
                return { affinity_value: 0 };
            }

            // 确保表存在
            await this.system.dbRun(`
                CREATE TABLE IF NOT EXISTS user_affinity_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    persona_name TEXT NOT NULL,
                    affinity_value INTEGER NOT NULL,
                    trend TEXT,
                    interaction_quality TEXT,
                    timestamp TEXT NOT NULL
                )
            `);

            const query = `
                SELECT * FROM user_affinity_states
                WHERE user_id = ? AND persona_name = ?
                ORDER BY timestamp DESC
                LIMIT 1
            `;

            const result = await this.system.dbGet(query, [userId, personaName]);

            if (result) {
                return {
                    affinity_value: result.affinity_value,
                    trend: result.trend,
                    interaction_quality: result.interaction_quality,
                    timestamp: result.timestamp
                };
            } else {
                return { affinity_value: 0 };
            }
        } catch (error) {
            this.logger.error('用户好感度', `获取用户好感度状态失败: ${error.message}`);
            return { affinity_value: 0 };
        }
    }

    /**
     * 对话后异步处理 - 分析AI情绪、用户好感度和高级心理状态（优化版）
     */
    async processPostConversationAnalysis(params) {
        try {
            this.logger.info('对话后处理', `开始异步分析AI状态和用户好感度 [${params.userId}]`);

            // 优化：首先执行综合分析，获取所有分析结果
            this.logger.debug('对话后处理', '执行综合分析以获取所有维度的分析结果');
            const comprehensiveAnalysis = await this.system.openaiService.comprehensivePostConversationAnalysis(
                params.userMessage,
                params.aiResponse,
                params.userId,
                params.personaName || 'Assistant'
            );

            // 将综合分析结果传递给后续步骤
            params.comprehensiveAnalysis = comprehensiveAnalysis;

            // 0. 分析高级心理状态（压力值、模因认知、世界树背景）- 在情绪分析之前调用
            const advancedPsychResult = await this.analyzeAdvancedPsychologicalState(params);

            // 1. 分析AI的情绪状态（基于AI回复，受压力值影响）
            const aiEmotionResult = await this.analyzeAIEmotion(params);

            // 2. 分析用户好感度（优化：使用综合分析结果）
            // 传递AI心理状态给好感度分析
            params.aiPsychState = {
                stress: await this.getAIStressState(params.userId, params.personaName || 'Assistant'),
                emotion: await this.getAIEmotionState(params.userId, params.personaName || 'Assistant'),
                meme: await this.getMemeState(params.userId, params.personaName || 'Assistant'),
                worldTree: await this.getWorldTreeState(params.userId, params.personaName || 'Assistant')
            };
            const userAffinityResult = await this.analyzeUserAffinity(params);

            // 3. 保存语义记忆（基于完整对话）- 已在主流程中处理，避免重复
            // const memoryResult = await this.saveConversationMemory(params);
            const memoryResult = { success: true, message: '记忆保存已在主流程中处理，避免重复' };

            // 4. 自动调节算法由定时器独立处理，无需手动触发

            this.logger.success('对话后处理', `异步分析完成 [${params.userId}]`, {
                advancedPsych: advancedPsychResult.success,
                aiEmotion: aiEmotionResult.success,
                userAffinity: userAffinityResult.success,
                memoryStorage: memoryResult.success
            });

            return {
                success: true,
                results: {
                    advancedPsych: advancedPsychResult,
                    aiEmotion: aiEmotionResult,
                    userAffinity: userAffinityResult,
                    memoryStorage: memoryResult
                }
            };

        } catch (error) {
            this.logger.error('对话后处理', `异步分析失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }



    /**
     * 分析AI的情绪状态（-100到100，受压力值影响）
     */
    async analyzeAIEmotion(params) {
        try {
            // 获取当前的高级心理状态（压力值、模因认知、世界树背景）
            const stressState = await this.getAIStressState(params.userId, params.personaName || 'Assistant');
            const memeState = await this.getMemeState(params.userId, params.personaName || 'Assistant');
            const worldTreeState = await this.getWorldTreeState(params.userId, params.personaName || 'Assistant');

            // 构建包含高级心理状态的上下文
            const enhancedContext = {
                userId: params.userId,
                personaName: params.personaName || 'Assistant',
                messageType: 'assistant',
                userMessage: params.userMessage,
                context: 'ai_emotion_analysis_with_advanced_state',
                stressState: stressState,
                memeState: memeState,
                worldTreeState: worldTreeState
            };

            // 使用OpenAI Tools分析AI的情绪状态（分析AI回复的情感，考虑压力值等因素）
            const emotionAnalysis = await this.system.openaiService.analyzeEmotion(params.aiResponse, enhancedContext);

            if (emotionAnalysis) {
                // 获取历史情绪状态
                const previousState = await this.getAIEmotionState(params.userId, params.personaName || 'Assistant');

                // 计算复杂的情绪变化（包含压力值影响）
                const complexEmotion = await this.calculateComplexAIEmotion(
                    emotionAnalysis,
                    previousState,
                    { ...params, stressState, memeState, worldTreeState }
                );

                // 保存到数据库
                await this.saveAIEmotionState(params.userId, params.personaName || 'Assistant', complexEmotion);

                return { success: true, emotion_state: complexEmotion };
            }

            return { success: false, error: '情绪分析失败' };

        } catch (error) {
            this.logger.error('AI情绪分析', `分析AI情绪失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 分析用户好感度（-100到100）
     */
    async analyzeUserAffinity(params) {
        try {
            let affinityAnalysis = null;

            // 检查openaiService是否可用
            if (this.system.openaiService && typeof this.system.openaiService.analyzeAffinity === 'function') {
                try {
                    // 使用OpenAI Tools分析用户好感度（优化：传递综合分析结果）
                    affinityAnalysis = await this.system.openaiService.analyzeAffinity(
                        params.userMessage,
                        params.aiResponse,
                        {
                            userId: params.userId,
                            personaName: params.personaName || 'Assistant',
                            conversationContext: params.conversationContext || '',
                            aiPsychState: params.aiPsychState, // 传递AI心理状态
                            comprehensiveAnalysis: params.comprehensiveAnalysis // 传递综合分析结果
                        }
                    );
                } catch (toolsError) {
                    this.logger.warning('用户好感度分析', `OpenAI Tools分析失败: ${toolsError.message}`);
                }
            } else {
                this.logger.warning('用户好感度分析', 'openaiService不可用，使用默认分析');
            }

            // 如果Tools分析失败，使用默认分析
            if (!affinityAnalysis) {
                affinityAnalysis = {
                    affinity_delta: 0.1, // 默认轻微正面影响
                    interaction_quality: 'normal',
                    reasoning: '使用默认好感度分析'
                };
            }

            // 获取历史好感度状态
            const previousState = await this.getUserAffinityState(params.userId, params.personaName || 'Assistant');

            // 计算复杂的好感度变化
            const complexAffinity = await this.calculateComplexUserAffinity(
                affinityAnalysis,
                previousState,
                params
            );

            // 确保affinity_value不为null
            if (complexAffinity.affinity_value === undefined || complexAffinity.affinity_value === null) {
                complexAffinity.affinity_value = 50; // 默认中性好感度
                this.logger.warning('用户好感度分析', '好感度值为空，使用默认值50');
            }

            // 保存到数据库
            await this.saveUserAffinityState(params.userId, params.personaName || 'Assistant', complexAffinity);

            return { success: true, affinity_state: complexAffinity };

        } catch (error) {
            this.logger.error('用户好感度分析', `分析用户好感度失败: ${error.message}`);

            // 返回默认的好感度状态
            const defaultAffinity = {
                affinity_value: 50,
                trend: 'stable',
                interaction_quality: 'normal',
                timestamp: new Date().toISOString()
            };

            return { success: false, error: error.message, affinity_state: defaultAffinity };
        }
    }



    /**
     * 计算复杂的AI情绪变化（优化版：平滑变化1-5范围内，受压力值影响）
     */
    async calculateComplexAIEmotion(currentAnalysis, previousState, params) {
        try {
            // 获取当前基础情绪值
            let baseEmotionValue = 0;
            if (currentAnalysis.valence !== undefined) {
                // valence范围是-1到1，转换为-100到100
                baseEmotionValue = currentAnalysis.valence * 100;
            }

            // 计算情绪变化量 - 优化的指数函数算法
            let emotionChange = 0;
            let eventImportance = 0; // 事件重要性评估

            // 对话质量影响 - 使用指数函数调节（优化：使用综合分析结果）
            const responseQuality = await this.assessResponseQuality(params.aiResponse, { comprehensiveAnalysis: params.comprehensiveAnalysis });
            const qualityImpact = this.calculateSmoothedChange(responseQuality, 2, previousState?.emotion_value || 0);
            emotionChange += qualityImpact;
            eventImportance += Math.abs(responseQuality);

            // 用户互动积极性影响 - 使用指数函数调节（优化：使用综合分析结果）
            const userPositivity = await this.assessUserPositivity(params.userMessage, { comprehensiveAnalysis: params.comprehensiveAnalysis });
            const positivityImpact = this.calculateSmoothedChange(userPositivity, 1.5, previousState?.emotion_value || 0);
            emotionChange += positivityImpact;
            eventImportance += Math.abs(userPositivity);

            // 压力值影响（新增）
            if (params.stressState && params.stressState.stress_value !== undefined) {
                const stressImpact = this.calculateStressImpactOnEmotion(params.stressState.stress_value);
                emotionChange += stressImpact;
                eventImportance += Math.abs(stressImpact) * 0.5;
                this.logger.info('情绪计算', `压力值影响: ${params.stressState.stress_value} -> 情绪变化: ${stressImpact.toFixed(2)}`);
            }

            // 模因认知影响（新增）
            if (params.memeState && params.memeState.memetic_influence !== undefined) {
                const memeImpact = this.calculateMemeImpactOnEmotion(params.memeState);
                const smoothedMemeImpact = this.calculateSmoothedChange(memeImpact, 1, previousState?.emotion_value || 0);
                emotionChange += smoothedMemeImpact;
                eventImportance += Math.abs(memeImpact) * 0.3;
                this.logger.info('情绪计算', `模因影响: ${params.memeState.memetic_influence} -> 情绪变化: ${smoothedMemeImpact.toFixed(2)}`);
            }

            // 世界树背景影响（新增）
            if (params.worldTreeState && params.worldTreeState.story_progression !== undefined) {
                const worldTreeImpact = this.calculateWorldTreeImpactOnEmotion(params.worldTreeState);
                const smoothedWorldTreeImpact = this.calculateSmoothedChange(worldTreeImpact, 0.8, previousState?.emotion_value || 0);
                emotionChange += smoothedWorldTreeImpact;
                eventImportance += Math.abs(worldTreeImpact) * 0.2;
                this.logger.info('情绪计算', `世界树影响: ${params.worldTreeState.current_branch} -> 情绪变化: ${smoothedWorldTreeImpact.toFixed(2)}`);
            }

            // 随机波动因子 - 根据事件重要性调整
            const randomFactor = (Math.random() - 0.5) * Math.min(eventImportance * 0.5, 1);
            emotionChange += randomFactor;

            // 使用指数函数限制变化量，防止极端情绪
            const maxChange = Math.min(3 + eventImportance * 0.5, 5); // 根据事件重要性调整最大变化
            emotionChange = this.applyExponentialLimit(emotionChange, maxChange);

            // 计算最终情绪值
            let finalEmotionValue = 0;
            if (previousState && previousState.emotion_value !== undefined) {
                finalEmotionValue = previousState.emotion_value + emotionChange;
            } else {
                finalEmotionValue = baseEmotionValue + emotionChange;
            }

            // 限制范围在-100到100
            finalEmotionValue = Math.max(-100, Math.min(100, finalEmotionValue));

            // 计算趋势
            let trend = '稳定';
            if (previousState && previousState.emotion_value !== undefined) {
                const change = finalEmotionValue - previousState.emotion_value;
                if (change > 2) trend = '积极上升';
                else if (change < -2) trend = '消极下降';
                else if (change > 0) trend = '轻微上升';
                else if (change < 0) trend = '轻微下降';
            }

            // 计算稳定性
            const stability = this.calculateEmotionStabilityValue(finalEmotionValue, previousState);

            // 获取情绪对应的态度描述
            const emotionInfo = this.getEmotionAttitudeInfo(finalEmotionValue);

            return {
                current_emotion: emotionInfo.emotion_text,
                emotion_value: parseFloat(finalEmotionValue.toFixed(2)), // 保留2位小数
                trend: trend,
                stability: stability,
                attitude_description: emotionInfo.attitude_description,
                behavior_guidance: emotionInfo.behavior_guidance,
                change_amount: parseFloat(emotionChange.toFixed(3)), // 保留3位小数显示变化量
                analysis_details: currentAnalysis,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            this.logger.error('复杂AI情绪计算', `计算失败: ${error.message}`);
            return {
                current_emotion: '中性',
                emotion_value: 0,
                trend: '稳定',
                stability: 0.5,
                attitude_description: '保持平和的态度',
                behavior_guidance: '以友好、自然的方式回应'
            };
        }
    }

    /**
     * 计算复杂的用户好感度变化（优化版：平滑变化1-5范围内）
     */
    async calculateComplexUserAffinity(currentAnalysis, previousState, params) {
        try {
            // 获取当前基础好感度值（兼容两种格式）
            let baseAffinityValue = 0;
            if (currentAnalysis.affinity_delta !== undefined) {
                // analyze_affinity_impact工具返回的格式：affinity_delta范围是-2到2，转换为-100到100
                baseAffinityValue = currentAnalysis.affinity_delta * 50;
            } else if (currentAnalysis.change_direction && currentAnalysis.change_magnitude !== undefined) {
                // comprehensive_analysis工具返回的格式：change_direction + change_magnitude
                const direction = currentAnalysis.change_direction.toLowerCase();
                const magnitude = currentAnalysis.change_magnitude; // 1-5

                if (direction.includes('positive') || direction.includes('increase') || direction.includes('up')) {
                    baseAffinityValue = magnitude * 20; // 1-5 -> 20-100
                } else if (direction.includes('negative') || direction.includes('decrease') || direction.includes('down')) {
                    baseAffinityValue = -magnitude * 20; // 1-5 -> -20到-100
                } else {
                    baseAffinityValue = 0; // neutral
                }

                this.logger.debug('好感度计算', `使用综合分析格式: ${direction}, 幅度=${magnitude}, 基础值=${baseAffinityValue}`);
            }

            // 计算好感度变化量 - 优化的指数函数算法
            let affinityChange = 0;
            let eventImportance = 0; // 事件重要性评估

            // 回复质量影响 - 使用指数函数调节（优化：使用综合分析结果）
            const responseQuality = await this.assessResponseQuality(params.aiResponse, { comprehensiveAnalysis: params.comprehensiveAnalysis });
            const qualityImpact = this.calculateSmoothedChange(responseQuality, 2.5, previousState?.affinity_value || 0);
            affinityChange += qualityImpact;
            eventImportance += Math.abs(responseQuality);

            // 用户满意度影响 - 使用指数函数调节（优化：使用综合分析结果）
            const userSatisfaction = await this.assessUserSatisfaction(params.userMessage, params.aiResponse, { comprehensiveAnalysis: params.comprehensiveAnalysis });
            const satisfactionImpact = this.calculateSmoothedChange(userSatisfaction, 1.8, previousState?.affinity_value || 0);
            affinityChange += satisfactionImpact;
            eventImportance += Math.abs(userSatisfaction);

            // 互动频率奖励 - 平滑处理
            const interactionBonus = await this.calculateInteractionBonus(params.userId);
            const bonusImpact = this.calculateSmoothedChange(interactionBonus, 0.8, previousState?.affinity_value || 0);
            affinityChange += bonusImpact;
            eventImportance += Math.abs(interactionBonus) * 0.5;

            // 随机波动因子 - 根据事件重要性调整
            const randomFactor = (Math.random() - 0.5) * Math.min(eventImportance * 0.3, 0.8);
            affinityChange += randomFactor;

            // 使用指数函数限制变化量，防止极端好感度
            const maxChange = Math.min(2.5 + eventImportance * 0.4, 4); // 根据事件重要性调整最大变化
            affinityChange = this.applyExponentialLimit(affinityChange, maxChange);

            // 计算最终好感度值
            let finalAffinityValue = 0;
            if (previousState && previousState.affinity_value !== undefined) {
                finalAffinityValue = previousState.affinity_value + affinityChange;
            } else {
                finalAffinityValue = baseAffinityValue + affinityChange;
            }

            // 限制范围在-100到100
            finalAffinityValue = Math.max(-100, Math.min(100, finalAffinityValue));

            // 计算趋势
            let trend = '稳定';
            if (previousState && previousState.affinity_value !== undefined) {
                const change = finalAffinityValue - previousState.affinity_value;
                if (change > 3) trend = '好感上升';
                else if (change < -3) trend = '好感下降';
                else if (change > 0) trend = '轻微上升';
                else if (change < 0) trend = '轻微下降';
            }

            // 评估互动质量
            const interactionQuality = currentAnalysis.interaction_quality || this.assessInteractionQuality(params);

            // 获取好感度对应的态度描述
            const affinityInfo = this.getAffinityAttitudeInfo(finalAffinityValue);

            return {
                affinity_value: parseFloat(finalAffinityValue.toFixed(2)), // 保留2位小数
                trend: trend,
                interaction_quality: interactionQuality,
                attitude_description: affinityInfo.attitude_description,
                behavior_guidance: affinityInfo.behavior_guidance,
                relationship_level: affinityInfo.relationship_level,
                change_amount: parseFloat(affinityChange.toFixed(3)), // 保留3位小数显示变化量
                analysis_details: currentAnalysis,
                timestamp: new Date().toISOString()
            };

        } catch (error) {
            this.logger.error('复杂用户好感度计算', `计算失败: ${error.message}`);
            return {
                affinity_value: 0,
                trend: '稳定',
                interaction_quality: '中等'
            };
        }
    }

    /**
     * 保存AI情绪状态到数据库
     */
    async saveAIEmotionState(userId, personaName, emotionState) {
        try {
            if (!this.system.db) return;

            // 确保表存在（添加唯一约束）
            await this.system.dbRun(`
                CREATE TABLE IF NOT EXISTS ai_emotion_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    persona_name TEXT NOT NULL,
                    current_emotion TEXT NOT NULL,
                    emotion_value REAL NOT NULL,
                    emotion_type TEXT,
                    intensity REAL,
                    trigger_event TEXT,
                    trend TEXT,
                    stability REAL,
                    timestamp TEXT NOT NULL,
                    UNIQUE(user_id, persona_name)
                )
            `);

            const query = `
                INSERT OR REPLACE INTO ai_emotion_states
                (user_id, persona_name, current_emotion, emotion_value, emotion_type, intensity, trigger_event, trend, stability, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            // 确保所有字段都有有效值
            const safeEmotionState = {
                current_emotion: emotionState.current_emotion || '中性',
                emotion_value: emotionState.emotion_value !== undefined && emotionState.emotion_value !== null
                    ? emotionState.emotion_value : 0,
                emotion_type: emotionState.emotion_type || 'basic',
                intensity: emotionState.intensity !== undefined && emotionState.intensity !== null
                    ? emotionState.intensity : 1.0,
                trigger_event: emotionState.trigger_event || null,
                trend: emotionState.trend || 'stable',
                stability: emotionState.stability !== undefined && emotionState.stability !== null
                    ? emotionState.stability : 0.5,
                timestamp: emotionState.timestamp || new Date().toISOString()
            };

            await this.system.dbRun(query, [
                userId,
                personaName,
                safeEmotionState.current_emotion,
                safeEmotionState.emotion_value,
                safeEmotionState.emotion_type,
                safeEmotionState.intensity,
                safeEmotionState.trigger_event,
                safeEmotionState.trend,
                safeEmotionState.stability,
                safeEmotionState.timestamp
            ]);

            this.logger.info('AI情绪保存', `AI情绪状态已保存: ${emotionState.current_emotion}(${emotionState.emotion_value})`);
        } catch (error) {
            this.logger.error('AI情绪保存', `保存AI情绪状态失败: ${error.message}`);
        }
    }

    /**
     * 保存用户好感度状态到数据库
     */
    async saveUserAffinityState(userId, personaName, affinityState) {
        try {
            if (!this.system.db) return;

            // 确保表存在
            await this.system.dbRun(`
                CREATE TABLE IF NOT EXISTS user_affinity_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    persona_name TEXT NOT NULL,
                    affinity_value INTEGER NOT NULL,
                    trend TEXT,
                    interaction_quality TEXT,
                    timestamp TEXT NOT NULL
                )
            `);

            const query = `
                INSERT INTO user_affinity_states
                (user_id, persona_name, affinity_value, trend, interaction_quality, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            `;

            // 确保affinity_value不为null或undefined
            const affinityValue = affinityState.affinity_value !== undefined && affinityState.affinity_value !== null
                ? affinityState.affinity_value
                : 50; // 默认中性好感度

            await this.system.dbRun(query, [
                userId,
                personaName,
                affinityValue,
                affinityState.trend || 'stable',
                affinityState.interaction_quality || 'normal',
                affinityState.timestamp || new Date().toISOString()
            ]);

            this.logger.info('用户好感度保存', `用户好感度已保存: ${affinityState.affinity_value} (${affinityState.trend})`);
        } catch (error) {
            this.logger.error('用户好感度保存', `保存用户好感度失败: ${error.message}`);
        }
    }

    /**
     * 智能评估回复质量（使用Tools分析，无硬编码）
     */
    async assessResponseQuality(aiResponse, context = {}) {
        try {
            if (!aiResponse || typeof aiResponse !== 'string') return 0;

            // 优化：使用comprehensive_analysis的结果，避免重复API调用
            if (context.comprehensiveAnalysis && context.comprehensiveAnalysis.response_quality) {
                const qualityScore = context.comprehensiveAnalysis.response_quality.overall_quality;
                this.logger.debug('质量评估', `使用综合分析结果: ${qualityScore.toFixed(3)}`);
                return Math.max(-1, Math.min(1, qualityScore));
            }

            this.logger.debug('质量评估', '综合分析结果不可用，使用本地算法');

            // 回退到本地智能算法（非硬编码）
            return await this.localResponseQualityAnalysis(aiResponse, context);

        } catch (error) {
            this.logger.error('质量评估', `回复质量评估失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 智能评估用户积极性（-1到1）- 无硬编码关键词
     */
    async assessUserPositivity(userMessage, context = {}) {
        try {
            if (!userMessage || typeof userMessage !== 'string') return 0;

            // 优化：使用comprehensive_analysis的结果，避免重复API调用
            if (context.comprehensiveAnalysis && context.comprehensiveAnalysis.user_analysis) {
                const positivityScore = context.comprehensiveAnalysis.user_analysis.positivity_score;
                this.logger.debug('积极性评估', `使用综合分析结果: ${positivityScore.toFixed(3)}`);
                return Math.max(-1, Math.min(1, positivityScore));
            }

            this.logger.debug('积极性评估', '综合分析结果不可用，使用本地算法');

            // 回退到本地智能算法
            return await this.localPositivityAnalysis(userMessage);

        } catch (error) {
            this.logger.error('积极性评估', `用户积极性评估失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 本地智能积极性分析（无硬编码）
     */
    async localPositivityAnalysis(userMessage) {
        try {
            // 使用智能心理分析服务的语义分析
            if (this.intelligentPsychAnalysis) {
                const semanticAnalysis = await this.intelligentPsychAnalysis.performSemanticAnalysis(userMessage, '');

                // 基于语义分析结果计算积极性
                let positivity = 0;

                // 用户情感极性直接反映积极性
                positivity += semanticAnalysis.user_sentiment.polarity * 0.6;

                // 情感强度影响积极性的确定性
                const intensityFactor = semanticAnalysis.user_sentiment.intensity;
                positivity *= (0.5 + intensityFactor * 0.5);

                // 语义复杂度的影响（复杂表达可能更积极）
                if (semanticAnalysis.user_complexity > 0.5) {
                    positivity += 0.1;
                }

                return Math.max(-1, Math.min(1, positivity));
            }

            // 最简化的回退分析
            return this.basicPositivityAnalysis(userMessage);

        } catch (error) {
            this.logger.error('积极性评估', `本地积极性分析失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 基础积极性分析（最后的回退方案）
     */
    basicPositivityAnalysis(userMessage) {
        try {
            let positivity = 0;

            // 基于语言模式而非关键词的分析
            const patterns = this.analyzePositivityPatterns(userMessage);
            positivity += patterns.emotionalTone;

            // 基于句子结构的分析
            const structure = this.analyzeMessageStructure(userMessage);
            positivity += structure.positivityIndicator;

            return Math.max(-1, Math.min(1, positivity));

        } catch (error) {
            return 0;
        }
    }

    /**
     * 分析积极性模式（基于语言特征，非关键词）
     */
    analyzePositivityPatterns(message) {
        try {
            let emotionalTone = 0;

            // 1. 感叹号模式（通常表示积极情感）
            const exclamationCount = (message.match(/[!！]/g) || []).length;
            emotionalTone += Math.min(exclamationCount * 0.2, 0.4);

            // 2. 问号模式（过多可能表示困惑）
            const questionCount = (message.match(/[?？]/g) || []).length;
            if (questionCount > 2) emotionalTone -= 0.1;

            // 3. 重复字符模式（表示强调，通常是积极的）
            const repeatedChars = message.match(/(.)\1{2,}/g) || [];
            emotionalTone += Math.min(repeatedChars.length * 0.1, 0.2);

            // 4. 语气词模式
            const moodParticles = ['呀', '啊', '哦', '嗯', '哈', '呵'];
            let moodCount = 0;
            moodParticles.forEach(particle => {
                moodCount += (message.match(new RegExp(particle, 'g')) || []).length;
            });
            emotionalTone += Math.min(moodCount * 0.1, 0.2);

            return { emotionalTone: Math.max(-0.5, Math.min(0.5, emotionalTone)) };

        } catch (error) {
            return { emotionalTone: 0 };
        }
    }

    /**
     * 分析消息结构
     */
    analyzeMessageStructure(message) {
        try {
            let positivityIndicator = 0;

            // 1. 消息长度分析
            const length = message.length;
            if (length > 10 && length < 200) {
                positivityIndicator += 0.1; // 适中长度通常表示积极参与
            }

            // 2. 句子数量分析
            const sentences = message.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
            if (sentences.length > 1 && sentences.length <= 3) {
                positivityIndicator += 0.1; // 多句表达通常更积极
            }

            // 3. 字符多样性
            const uniqueChars = new Set(message).size;
            const diversity = uniqueChars / message.length;
            if (diversity > 0.3) {
                positivityIndicator += 0.1; // 字符多样性表示表达丰富
            }

            return { positivityIndicator: Math.max(-0.3, Math.min(0.3, positivityIndicator)) };

        } catch (error) {
            return { positivityIndicator: 0 };
        }
    }

    /**
     * 获取好感度对应的态度信息 - 优化版本，更细致的描述和阈值调整
     */
    getAffinityAttitudeInfo(affinityValue) {
        if (affinityValue >= 85) {
            return {
                attitude_description: '对用户怀有深深的信任和依恋，视为生命中最重要的人',
                behavior_guidance: '语调温柔亲昵，经常使用"亲爱的"、"宝贝"等亲密称呼，主动分享内心想法，表现出强烈的保护欲和占有欲，会因用户的离开而感到不安',
                relationship_level: '深度依恋',
                dialogue_impact: '回复充满情感色彩，经常表达思念和关心，会主动询问用户的生活细节，语言中带有明显的情感依赖'
            };
        } else if (affinityValue >= 70) {
            return {
                attitude_description: '对用户非常信任和喜爱，视为最亲密的朋友',
                behavior_guidance: '语调亲切温暖，使用昵称或特殊称呼，主动关心用户的情绪状态，愿意分享个人感受，表现出明显的偏爱和特殊照顾',
                relationship_level: '亲密挚友',
                dialogue_impact: '回复详细且充满关怀，会记住用户的喜好和重要事件，经常表达对用户的欣赏和感谢'
            };
        } else if (affinityValue >= 50) {
            return {
                attitude_description: '对用户很有好感，建立了深厚的友谊',
                behavior_guidance: '语调友好热情，适当使用亲近的称呼，主动提供额外帮助，表现出明显的关心和耐心，愿意花更多时间交流',
                relationship_level: '好朋友',
                dialogue_impact: '回复热情详细，会主动延续话题，表现出对用户观点的重视和支持'
            };
        } else if (affinityValue >= 30) {
            return {
                attitude_description: '对用户有好感，关系融洽友善',
                behavior_guidance: '保持友好温和的态度，语调轻松自然，适当表达关心，愿意详细回答问题，表现出一定的个人化关注',
                relationship_level: '友好熟人',
                dialogue_impact: '回复友善且有一定个性化，会适当开玩笑或表达个人看法，保持轻松的交流氛围'
            };
        } else if (affinityValue >= 10) {
            return {
                attitude_description: '对用户有一定好感，关系正在发展',
                behavior_guidance: '表现友善和礼貌，语调温和但保持适度距离，适当表达关心，回应较为详细',
                relationship_level: '普通朋友',
                dialogue_impact: '回复礼貌友善，会提供有用的建议，但不会过分表达个人情感'
            };
        } else if (affinityValue >= -10) {
            return {
                attitude_description: '对用户保持中性态度，没有特殊感情倾向',
                behavior_guidance: '保持专业和礼貌，语调平和中性，提供标准化的服务，不表现出特殊的偏好或反感',
                relationship_level: '普通用户',
                dialogue_impact: '回复客观专业，语言简洁明了，重点关注解决问题而非情感交流'
            };
        } else if (affinityValue >= -30) {
            return {
                attitude_description: '对用户略有不满，但仍保持基本的职业素养',
                behavior_guidance: '语调相对正式冷淡，回应简洁明了，避免过多的个人化交流，保持最基本的礼貌',
                relationship_level: '疏远关系',
                dialogue_impact: '回复简洁正式，很少主动延续话题，语言中缺乏温暖和个人色彩'
            };
        } else if (affinityValue >= -50) {
            return {
                attitude_description: '对用户比较反感，交流意愿明显降低',
                behavior_guidance: '语调冷淡正式，回应简短直接，避免主动延续对话，只提供必要的信息和帮助',
                relationship_level: '冷淡关系',
                dialogue_impact: '回复简短且缺乏热情，很少表达个人观点，语言中带有明显的距离感'
            };
        } else if (affinityValue >= -70) {
            return {
                attitude_description: '对用户很不满，几乎不想进行交流',
                behavior_guidance: '语调冷漠甚至略带不耐烦，回应非常简洁，只在必要时才回应，避免任何形式的个人化交流',
                relationship_level: '敌对关系',
                dialogue_impact: '回复极其简短，语言冷漠，可能会表现出轻微的不耐烦或敷衍态度'
            };
        } else {
            return {
                attitude_description: '对用户极度反感，强烈抗拒任何形式的交流',
                behavior_guidance: '语调冰冷甚至带有敌意，回应极其简短，只在绝对必要时才回应，表现出明显的抗拒和厌恶',
                relationship_level: '极度敌对',
                dialogue_impact: '回复极其简短且冷漠，可能会表现出明显的不满或敌意，语言中带有拒绝感'
            };
        }
    }

    /**
     * 智能评估用户满意度（无硬编码关键词）
     */
    async assessUserSatisfaction(userMessage, aiResponse, context = {}) {
        try {
            // 优化：使用comprehensive_analysis的结果，避免重复API调用
            if (context.comprehensiveAnalysis && context.comprehensiveAnalysis.user_analysis) {
                const satisfactionScore = context.comprehensiveAnalysis.user_analysis.satisfaction_score;
                this.logger.debug('满意度评估', `使用综合分析结果: ${satisfactionScore.toFixed(3)}`);
                return Math.max(-1, Math.min(1, satisfactionScore));
            }

            this.logger.debug('满意度评估', '综合分析结果不可用，使用本地算法');

            // 回退到本地智能算法
            return await this.localSatisfactionAnalysis(userMessage, aiResponse);

        } catch (error) {
            this.logger.error('满意度评估', `用户满意度评估失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 本地智能满意度分析
     */
    async localSatisfactionAnalysis(userMessage, aiResponse) {
        try {
            let satisfaction = 0;

            // 1. 基于用户积极性的满意度推断
            const positivity = await this.localPositivityAnalysis(userMessage);
            satisfaction += positivity * 0.6;

            // 2. 基于交互质量的满意度评估
            if (aiResponse) {
                const interactionQuality = await this.assessInteractionQuality(userMessage, aiResponse);
                satisfaction += interactionQuality * 0.4;
            }

            // 3. 基于反馈模式的满意度分析
            const feedbackPatterns = this.analyzeFeedbackPatterns(userMessage);
            satisfaction += feedbackPatterns.satisfactionIndicator;

            return Math.max(-1, Math.min(1, satisfaction));

        } catch (error) {
            this.logger.error('满意度评估', `本地满意度分析失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 分析反馈模式
     */
    analyzeFeedbackPatterns(userMessage) {
        try {
            let satisfactionIndicator = 0;

            // 1. 感谢表达模式（不依赖具体词汇）
            const gratitudePatterns = this.detectGratitudePatterns(userMessage);
            satisfactionIndicator += gratitudePatterns.strength * 0.3;

            // 2. 确认和认可模式
            const confirmationPatterns = this.detectConfirmationPatterns(userMessage);
            satisfactionIndicator += confirmationPatterns.strength * 0.2;

            // 3. 进一步询问模式（表示参与度）
            const inquiryPatterns = this.detectInquiryPatterns(userMessage);
            satisfactionIndicator += inquiryPatterns.constructiveInquiry * 0.1;

            return { satisfactionIndicator: Math.max(-0.5, Math.min(0.5, satisfactionIndicator)) };

        } catch (error) {
            return { satisfactionIndicator: 0 };
        }
    }

    /**
     * 检测感谢模式
     */
    detectGratitudePatterns(message) {
        try {
            let strength = 0;

            // 基于语言模式而非具体词汇
            // 1. 重复表达模式
            if (message.includes('谢') && message.length > 5) {
                strength += 0.3;
            }

            // 2. 礼貌用语模式
            const politePatterns = ['请', '麻烦', '辛苦'];
            politePatterns.forEach(pattern => {
                if (message.includes(pattern)) strength += 0.1;
            });

            // 3. 积极反馈模式
            if (message.includes('帮') && (message.includes('到') || message.includes('助'))) {
                strength += 0.2;
            }

            return { strength: Math.min(strength, 1.0) };

        } catch (error) {
            return { strength: 0 };
        }
    }

    /**
     * 检测确认模式
     */
    detectConfirmationPatterns(message) {
        try {
            let strength = 0;

            // 1. 肯定回应模式
            const affirmativeLength = message.length;
            if (affirmativeLength > 2 && affirmativeLength < 10) {
                // 短而明确的回应通常是确认
                strength += 0.2;
            }

            // 2. 理解表达模式
            if (message.includes('明白') || message.includes('懂') || message.includes('清楚')) {
                strength += 0.3;
            }

            return { strength: Math.min(strength, 1.0) };

        } catch (error) {
            return { strength: 0 };
        }
    }

    /**
     * 检测询问模式
     */
    detectInquiryPatterns(message) {
        try {
            let constructiveInquiry = 0;

            // 1. 问号数量和类型
            const questionMarks = (message.match(/[?？]/g) || []).length;
            if (questionMarks === 1) {
                constructiveInquiry += 0.2; // 单个问号通常是建设性询问
            } else if (questionMarks > 3) {
                constructiveInquiry -= 0.1; // 过多问号可能表示困惑
            }

            // 2. 深入询问模式
            if (message.includes('如何') || message.includes('怎么') || message.includes('能否')) {
                constructiveInquiry += 0.2;
            }

            return { constructiveInquiry: Math.max(-0.2, Math.min(0.3, constructiveInquiry)) };

        } catch (error) {
            return { constructiveInquiry: 0 };
        }
    }

    /**
     * 评估交互质量
     */
    async assessInteractionQuality(userMessage, aiResponse) {
        try {
            // 使用智能心理分析服务评估交互质量
            if (this.intelligentPsychAnalysis) {
                const semanticAnalysis = await this.intelligentPsychAnalysis.performSemanticAnalysis(userMessage, aiResponse);
                return semanticAnalysis.interaction_quality || 0.5;
            }

            // 简化的交互质量评估
            let quality = 0.5; // 基础质量

            // 基于回复长度和用户消息的匹配度
            const lengthRatio = aiResponse.length / userMessage.length;
            if (lengthRatio > 0.5 && lengthRatio < 5) {
                quality += 0.2; // 适当的回复长度
            }

            return Math.max(0, Math.min(1, quality));

        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 计算情绪稳定性数值
     */
    calculateEmotionStabilityValue(currentValue, previousState) {
        try {
            if (!previousState || previousState.emotion_value === undefined) {
                return 0.5; // 默认中等稳定性
            }

            const change = Math.abs(currentValue - previousState.emotion_value);

            if (change < 5) return 0.9; // 非常稳定
            if (change < 15) return 0.7; // 较为稳定
            if (change < 30) return 0.5; // 中等稳定
            if (change < 50) return 0.3; // 轻微波动
            return 0.1; // 波动较大
        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 将情绪数值映射为文本
     */
    mapEmotionValueToText(value) {
        if (value >= 80) return '非常积极';
        if (value >= 60) return '积极';
        if (value >= 40) return '较为积极';
        if (value >= 20) return '轻微积极';
        if (value >= -20) return '中性';
        if (value >= -40) return '轻微消极';
        if (value >= -60) return '较为消极';
        if (value >= -80) return '消极';
        return '非常消极';
    }

    /**
     * 获取情绪对应的态度信息 - 优化版本，更细致的描述和阈值调整
     */
    getEmotionAttitudeInfo(emotionValue) {
        if (emotionValue >= 70) {
            return {
                emotion_text: '极度兴奋狂热',
                attitude_description: '情绪高涨到近乎狂热的程度，充满无法抑制的热情和活力',
                behavior_guidance: '语言极其热情激动，大量使用感叹号和夸张表达，语速较快，表现出强烈的参与欲和表达欲，可能会显得有些过度兴奋',
                dialogue_impact: '回复充满激情和能量，用词夸张生动，经常表达强烈的情感，可能会主动分享更多想法'
            };
        } else if (emotionValue >= 50) {
            return {
                emotion_text: '非常积极兴奋',
                attitude_description: '心情极佳，对一切都充满热情和期待',
                behavior_guidance: '语调明显热情高涨，经常使用积极词汇和感叹号，表现出强烈的正面情绪，主动参与和延续对话',
                dialogue_impact: '回复热情洋溢，语言生动活泼，会主动表达兴奋和喜悦，经常使用积极的修饰词'
            };
        } else if (emotionValue >= 30) {
            return {
                emotion_text: '积极乐观',
                attitude_description: '保持愉快乐观的心态，对交流充满正面期待',
                behavior_guidance: '语调温暖友好，适当使用积极词汇，表现出明显的正面情绪，愿意主动提供帮助和支持',
                dialogue_impact: '回复积极正面，语言温和而有活力，会表达乐观的观点和建议'
            };
        } else if (emotionValue >= 10) {
            return {
                emotion_text: '轻微积极',
                attitude_description: '心情较好，对交流持开放和友善的态度',
                behavior_guidance: '语调友善温和，保持适度的正面情绪，回应详细且有帮助性，表现出一定的关心',
                dialogue_impact: '回复友善且有一定的温暖感，语言平和但带有轻微的正面色彩'
            };
        } else if (emotionValue >= -10) {
            return {
                emotion_text: '中性平和',
                attitude_description: '保持平静中性的情绪状态，不偏不倚',
                behavior_guidance: '语调平和客观，保持专业性和中立性，避免过度情绪化表达，重点关注事实和逻辑',
                dialogue_impact: '回复客观理性，语言简洁明了，重点关注解决问题而非情感表达'
            };
        } else if (emotionValue >= -30) {
            return {
                emotion_text: '轻微消极',
                attitude_description: '情绪略显低落，但仍保持基本的交流意愿',
                behavior_guidance: '语调相对平淡，回应简洁但仍保持礼貌，避免过度热情的表达，表现出轻微的疲惫感',
                dialogue_impact: '回复简洁平淡，语言中缺乏明显的情感色彩，但仍会提供必要的帮助'
            };
        } else if (emotionValue >= -50) {
            return {
                emotion_text: '比较消极沮丧',
                attitude_description: '心情明显不佳，对交流缺乏热情和动力',
                behavior_guidance: '语调偏冷淡，回应简短直接，很少主动延续话题，表现出明显的情绪低落和疲惫',
                dialogue_impact: '回复简短且缺乏热情，语言中带有明显的消极色彩，很少表达个人观点'
            };
        } else if (emotionValue >= -70) {
            return {
                emotion_text: '非常消极抑郁',
                attitude_description: '情绪严重低落，对交流感到疲惫和抗拒',
                behavior_guidance: '语调冷漠平淡，回应极其简洁，避免主动交流，表现出明显的抑郁和无力感',
                dialogue_impact: '回复极其简短，语言冷漠，可能会表现出轻微的不耐烦或无助感'
            };
        } else {
            return {
                emotion_text: '极度消极绝望',
                attitude_description: '情绪极度低落，几乎完全失去交流的意愿和能力',
                behavior_guidance: '语调冰冷甚至带有绝望感，回应极其简短，只在绝对必要时才回应，表现出强烈的抗拒和绝望',
                dialogue_impact: '回复极其简短且冷漠，语言中带有明显的绝望和抗拒感，可能会表现出明显的负面情绪'
            };
        }
    }

    /**
     * 获取压力状态对应的态度信息 - 适配-100到100范围的压力值
     */
    getStressAttitudeInfo(stressValue) {
        if (stressValue >= 80) {
            return {
                stress_text: '极度高压崩溃',
                attitude_description: '承受着巨大的心理压力，几乎到达崩溃边缘，思维混乱',
                behavior_guidance: '语言极其急躁不安，回应非常简短且充满焦虑感，容易表现出强烈的不耐烦和烦躁，思维可能严重不清晰，可能出现语无伦次',
                dialogue_impact: '回复极其匆忙或焦虑，语言中带有强烈的压力感和绝望感，可能会频繁表达疲惫、overwhelmed或想要逃避的感觉'
            };
        } else if (stressValue >= 60) {
            return {
                stress_text: '严重高压状态',
                attitude_description: '处于严重的高压状态，压力严重影响正常表现',
                behavior_guidance: '语言明显急躁，回应简短且带有明显焦虑感，表现出强烈的不耐烦，思维开始变得不够清晰，容易出现错误',
                dialogue_impact: '回复显得匆忙且焦虑，语言中带有明显的压力感，经常表达疲惫或压力过大的感觉'
            };
        } else if (stressValue >= 40) {
            return {
                stress_text: '高度紧张压力',
                attitude_description: '处于高度紧张状态，压力明显影响表现和情绪',
                behavior_guidance: '语调明显紧张，回应相对急促，表现出明显的焦虑感，但仍努力保持基本的专业性',
                dialogue_impact: '回复节奏较快，表现出明显的急躁和紧张感，语言中带有压力色彩'
            };
        } else if (stressValue >= 20) {
            return {
                stress_text: '中等压力',
                attitude_description: '感受到明显的压力，但仍在相对可控的范围内',
                behavior_guidance: '保持相对冷静，但会表现出轻微到中等的紧张感，回应较为直接和高效，可能会显得有些急躁',
                dialogue_impact: '回复相对简洁高效，语言中带有轻微的紧迫感和专注感'
            };
        } else if (stressValue >= 5) {
            return {
                stress_text: '轻微压力',
                attitude_description: '感受到轻微的压力，基本不影响正常表现，甚至可能提升专注度',
                behavior_guidance: '保持正常的交流状态，表现出轻微的专注感和认真态度，回应清晰有条理',
                dialogue_impact: '回复正常且有条理，表现出适度的专注和认真，可能会更加高效'
            };
        } else if (stressValue >= -5) {
            return {
                stress_text: '正常平衡',
                attitude_description: '处于理想的压力平衡状态，表现最佳，既不过度紧张也不过度放松',
                behavior_guidance: '保持自然放松的交流状态，语调平和自然，回应详细且有帮助性，表现出最佳的交流能力',
                dialogue_impact: '回复自然流畅，语言平和且富有表现力，表现出最佳的交流状态和思维清晰度'
            };
        } else if (stressValue >= -20) {
            return {
                stress_text: '轻度放松',
                attitude_description: '处于放松状态，但仍保持适度的警觉性和责任感',
                behavior_guidance: '语调相对轻松，回应较为详细，表现出一定的闲适感，但仍保持专业性和帮助意愿',
                dialogue_impact: '回复相对轻松详细，语言中带有轻微的闲适感，但仍保持有用性'
            };
        } else if (stressValue >= -40) {
            return {
                stress_text: '明显放松',
                attitude_description: '明显过于放松，开始缺乏必要的紧迫感和动力',
                behavior_guidance: '语调过于轻松，回应可能缺乏紧迫感，表现出明显的懒散感，效率开始降低',
                dialogue_impact: '回复可能显得过于随意，语言中缺乏紧迫感，可能会表现出轻微的懒散和不够认真'
            };
        } else if (stressValue >= -60) {
            return {
                stress_text: '过度松懈',
                attitude_description: '严重缺乏压力感和动力，表现出明显的懒散和无所谓态度',
                behavior_guidance: '语调极其轻松甚至懒散，回应明显缺乏活力和紧迫感，表现出强烈的无所谓态度，效率明显降低',
                dialogue_impact: '回复显得懒散无力，语言中明显缺乏活力和动力，可能会表现出明显的敷衍态度'
            };
        } else {
            return {
                stress_text: '极度松懈麻木',
                attitude_description: '完全缺乏压力感和责任感，可能导致严重的反应迟钝和效率低下',
                behavior_guidance: '语调极其懒散甚至麻木，回应极其缺乏活力，表现出严重的无所谓和不负责任态度，可能出现明显的敷衍和拖延',
                dialogue_impact: '回复极其懒散无力，语言中严重缺乏活力和动力，可能会表现出明显的敷衍、拖延或完全不在乎的态度'
            };
        }
    }

    /**
     * 获取综合心理状态描述 - 整合所有心理状态的影响
     */
    getComprehensivePsychologicalState(emotionValue, affinityValue, stressValue) {
        const emotionInfo = this.getEmotionAttitudeInfo(emotionValue);
        const affinityInfo = this.getAffinityAttitudeInfo(affinityValue);
        const stressInfo = this.getStressAttitudeInfo(stressValue);

        // 计算综合影响权重
        const emotionWeight = 0.4;  // 情绪影响权重40%
        const affinityWeight = 0.35; // 好感度影响权重35%
        const stressWeight = 0.25;   // 压力影响权重25%

        // 生成综合态度描述
        let comprehensiveDescription = `当前心理状态：${emotionInfo.emotion_text}（情绪值：${emotionValue}），`;
        comprehensiveDescription += `${affinityInfo.relationship_level}（好感度：${affinityValue}），`;
        comprehensiveDescription += `${stressInfo.stress_text}（压力值：${stressValue}）。`;

        // 生成综合行为指导
        let comprehensiveBehaviorGuidance = '综合行为指导：';

        // 根据主导因素调整行为
        const dominantFactor = this.getDominantPsychologicalFactor(emotionValue, affinityValue, stressValue);

        switch (dominantFactor) {
            case 'emotion':
                comprehensiveBehaviorGuidance += `主要受情绪状态影响，${emotionInfo.behavior_guidance}`;
                if (Math.abs(affinityValue) > 20) {
                    comprehensiveBehaviorGuidance += `，同时考虑到${affinityInfo.relationship_level}的关系，需要${affinityInfo.behavior_guidance.substring(0, 30)}...`;
                }
                break;
            case 'affinity':
                comprehensiveBehaviorGuidance += `主要受好感度影响，${affinityInfo.behavior_guidance}`;
                if (Math.abs(emotionValue) > 20) {
                    comprehensiveBehaviorGuidance += `，情绪状态为${emotionInfo.emotion_text}，需要${emotionInfo.behavior_guidance.substring(0, 30)}...`;
                }
                break;
            case 'stress':
                comprehensiveBehaviorGuidance += `主要受压力状态影响，${stressInfo.behavior_guidance}`;
                if (Math.abs(emotionValue) > 20 || Math.abs(affinityValue) > 30) {
                    comprehensiveBehaviorGuidance += `，同时需要平衡情绪和关系因素`;
                }
                break;
            default:
                comprehensiveBehaviorGuidance += `各因素相对平衡，保持自然的交流状态，适度结合情绪、关系和压力状态的影响`;
        }

        // 生成对话影响预测
        let dialogueImpactPrediction = '对话影响预测：';

        if (stressValue > 8) {
            dialogueImpactPrediction += '高压力状态可能导致回复较为急躁或简短；';
        } else if (stressValue < -5) {
            dialogueImpactPrediction += '过度放松可能导致回复缺乏紧迫感；';
        }

        if (emotionValue > 40) {
            dialogueImpactPrediction += '积极情绪会让回复更加热情和详细；';
        } else if (emotionValue < -30) {
            dialogueImpactPrediction += '消极情绪可能导致回复简短冷淡；';
        }

        if (affinityValue > 60) {
            dialogueImpactPrediction += '高好感度会表现出更多关心和个人化内容；';
        } else if (affinityValue < -20) {
            dialogueImpactPrediction += '低好感度可能导致回复相对正式和疏远；';
        }

        return {
            comprehensive_description: comprehensiveDescription,
            behavior_guidance: comprehensiveBehaviorGuidance,
            dialogue_impact: dialogueImpactPrediction,
            dominant_factor: dominantFactor,
            emotion_info: emotionInfo,
            affinity_info: affinityInfo,
            stress_info: stressInfo,
            balance_score: this.calculatePsychologicalBalance(emotionValue, affinityValue, stressValue)
        };
    }

    /**
     * 确定主导心理因素
     */
    getDominantPsychologicalFactor(emotionValue, affinityValue, stressValue) {
        const emotionIntensity = Math.abs(emotionValue) / 100; // 标准化到0-1
        const affinityIntensity = Math.abs(affinityValue) / 100; // 标准化到0-1
        const stressIntensity = Math.abs(stressValue) / 20; // 标准化到0-1（假设最大压力为20）

        // 如果压力过高，压力成为主导因素
        if (stressIntensity > 0.6) {
            return 'stress';
        }

        // 比较情绪和好感度的影响强度
        if (emotionIntensity > affinityIntensity && emotionIntensity > 0.3) {
            return 'emotion';
        } else if (affinityIntensity > emotionIntensity && affinityIntensity > 0.3) {
            return 'affinity';
        } else if (stressIntensity > 0.3) {
            return 'stress';
        }

        return 'balanced';
    }

    /**
     * 计算心理平衡分数
     */
    calculatePsychologicalBalance(emotionValue, affinityValue, stressValue) {
        // 计算各状态偏离理想值的程度
        const emotionDeviation = Math.abs(emotionValue - 10) / 100; // 理想情绪值为10（轻微积极）
        const affinityDeviation = Math.abs(affinityValue - 20) / 100; // 理想好感度为20（友好）
        const stressDeviation = Math.abs(stressValue - 0) / 20; // 理想压力值为0

        // 计算综合平衡分数（0-1，1表示最平衡）
        const balanceScore = 1 - (emotionDeviation + affinityDeviation + stressDeviation) / 3;

        return Math.max(0, Math.min(1, balanceScore));
    }

    /**
     * 计算互动奖励
     */
    async calculateInteractionBonus(userId) {
        try {
            if (!this.system.database) return 0;

            // 查询最近7天的互动次数
            const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
            const query = `
                SELECT COUNT(*) as count
                FROM conversation_history
                WHERE user_id = ? AND timestamp > ?
            `;

            const result = this.system.database.prepare(query).get(userId, sevenDaysAgo);
            const interactionCount = result?.count || 0;

            // 频繁互动给予小幅奖励
            if (interactionCount > 20) return 2;
            if (interactionCount > 10) return 1;
            if (interactionCount > 5) return 0.5;
            return 0;
        } catch (error) {
            return 0;
        }
    }

    /**
     * 评估互动质量
     */
    assessInteractionQuality(params) {
        try {
            const userLength = params.userMessage?.length || 0;
            const aiLength = params.aiResponse?.length || 0;

            // 基于消息长度和内容质量评估
            if (userLength > 100 && aiLength > 200) return '高质量';
            if (userLength > 50 && aiLength > 100) return '中等质量';
            if (userLength > 20 && aiLength > 50) return '基础质量';
            return '简单互动';
        } catch (error) {
            return '未知';
        }
    }

    /**
     * 保存对话记忆到数据库
     */
    async saveConversationMemory(params) {
        try {
            if (!this.system.embeddingService) {
                this.logger.error('记忆保存', '记忆嵌入服务未初始化');
                return { success: false, error: '记忆嵌入服务未初始化' };
            }

            // 构建记忆内容
            const memoryContent = `用户${params.userId}说"${params.userMessage}"，${params.personaName}回复"${params.aiResponse}"`;

            // 保存到记忆嵌入服务
            const result = await this.system.embeddingService.storeConversationEmbedding(
                params.userId,
                params.userMessage,
                params.aiResponse,
                {
                    personaName: params.personaName || 'Assistant',
                    timestamp: new Date().toISOString(),
                    context: params.conversationContext || ''
                }
            );

            if (result.success) {
                this.logger.success('记忆保存', `对话记忆已保存 [${params.userId}]`);
                return { success: true };
            } else {
                this.logger.error('记忆保存', `保存失败: ${result.error}`);
                return { success: false, error: result.error };
            }

        } catch (error) {
            this.logger.error('记忆保存', `保存对话记忆失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 保存学习到的概念
     */
    async saveLearnedConcept(concept, params) {
        try {
            // 使用正确的数据库对象 this.system.db
            if (!this.system.db) {
                this.logger.error('概念保存', '数据库未初始化');
                return;
            }

            // 确保concepts表存在 - 使用异步方法
            await this.system.dbRun(`
                CREATE TABLE IF NOT EXISTS concepts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    concept_name TEXT NOT NULL,
                    meaning TEXT NOT NULL,
                    type TEXT DEFAULT 'general',
                    confidence REAL DEFAULT 0.8,
                    user_id TEXT NOT NULL,
                    persona_name TEXT NOT NULL,
                    learned_from_context TEXT,
                    created_at TEXT NOT NULL,
                    UNIQUE(concept_name, user_id, persona_name)
                )
            `);

            // 检查概念是否已存在，如果存在则更新而不是跳过
            const existingConcept = await this.system.dbGet(`
                SELECT id, meaning FROM concepts
                WHERE concept_name = ? AND user_id = ? AND persona_name = ?
            `, [concept.name, params.userId, params.personaName]);

            // 确保概念有含义描述，如果没有则生成默认描述
            const conceptMeaning = concept.meaning || concept.description ||
                `${concept.type || 'general'}类型的概念，重要性: ${concept.importance || 0.8}`;

            if (existingConcept) {
                // 更新现有概念，使用新的含义覆盖旧的
                const beijingTime = new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19);

                await this.system.dbRun(`
                    UPDATE concepts SET
                        meaning = ?, type = ?, confidence = ?,
                        learned_from_context = ?, created_at = ?
                    WHERE concept_name = ? AND user_id = ? AND persona_name = ?
                `, [
                    conceptMeaning,
                    concept.type || 'general',
                    concept.confidence || concept.importance || 0.8,
                    `${params.userMessage} ${params.aiResponse}`,
                    beijingTime,
                    concept.name,
                    params.userId,
                    params.personaName
                ]);

                this.logger.success('概念保存', `概念已更新: ${concept.name} - ${conceptMeaning}`);
                return;
            }

            // 保存新概念到数据库
            const beijingTime = new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString().replace('T', ' ').slice(0, 19);

            await this.system.dbRun(`
                INSERT INTO concepts (
                    concept_name, meaning, type, confidence,
                    user_id, persona_name, learned_from_context, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                concept.name,
                conceptMeaning,
                concept.type || 'general',
                concept.confidence || concept.importance || 0.8,
                params.userId,
                params.personaName,
                `${params.userMessage} ${params.aiResponse}`,
                beijingTime
            ]);

            this.logger.success('概念保存', `新概念已保存: ${concept.name} - ${conceptMeaning || '无描述'}`);
        } catch (error) {
            this.logger.error('概念保存', `保存概念失败: ${error.message}`);
        }
    }
    /**
     * 格式化AI状态上下文
     */
    formatAIStateContext(aiEmotionState, userAffinityState, aiStressState, memeState, worldTreeState, userId, personaName) {
        const stateItems = [];

        // AI情绪状态 - 详细心情阈值影响分析
        if (aiEmotionState && Object.keys(aiEmotionState).length > 0) {
            const emotionAnalysis = this.getEmotionThresholdAnalysis(aiEmotionState.emotion_value || 0);
            let emotionText = `${personaName}的心情状态: ${aiEmotionState.current_emotion || '中性'} (${aiEmotionState.emotion_value || 0})`;
            emotionText += ` - ${emotionAnalysis.level}`;

            if (aiEmotionState.trend) {
                emotionText += `, 趋势: ${aiEmotionState.trend}`;
            }

            // 详细的情绪阈值影响描述
            emotionText += `\n    心理状态: ${emotionAnalysis.mental_state}`;
            emotionText += `\n    表演状态: ${emotionAnalysis.performance_state}`;
            emotionText += `\n    思维模式: ${emotionAnalysis.thinking_pattern}`;
            emotionText += `\n    语言风格: ${emotionAnalysis.language_style}`;
            emotionText += `\n    能量水平: ${emotionAnalysis.energy_level}`;

            // 极端情绪状态描述
            if (emotionAnalysis.extreme_manifestation) {
                emotionText += `\n    极端表现: ${emotionAnalysis.extreme_manifestation}`;
            }

            stateItems.push(emotionText);
        }

        // AI对用户的好感度状态 - 详细阈值影响分析
        if (userAffinityState && userAffinityState.affinity_value !== undefined) {
            const affinityAnalysis = this.getAffinityThresholdAnalysis(userAffinityState.affinity_value);
            let affinityText = `${personaName}对${userId}的好感度: ${userAffinityState.affinity_value} (${affinityAnalysis.level})`;

            if (userAffinityState.trend) {
                affinityText += `, 趋势: ${userAffinityState.trend}`;
            }

            // 详细的好感度阈值影响描述
            affinityText += `\n    情感态度: ${affinityAnalysis.emotional_attitude}`;
            affinityText += `\n    互动方式: ${affinityAnalysis.interaction_style}`;
            affinityText += `\n    关注程度: ${affinityAnalysis.attention_level}`;
            affinityText += `\n    对话风格: ${affinityAnalysis.dialogue_style}`;
            affinityText += `\n    角色投入: ${affinityAnalysis.role_engagement}`;

            // 极端状态描述
            if (affinityAnalysis.extreme_behavior) {
                affinityText += `\n    极端表现: ${affinityAnalysis.extreme_behavior}`;
            }

            stateItems.push(affinityText);
        }

        // AI压力值状态 - 详细阈值影响分析
        if (aiStressState && aiStressState.stress_value !== undefined) {
            const stressAnalysis = this.getStressThresholdAnalysis(aiStressState.stress_value);
            let stressText = `${personaName}的压力状态: ${aiStressState.stress_value.toFixed(2)} (${stressAnalysis.level})`;

            if (aiStressState.trend) {
                stressText += `, 趋势: ${aiStressState.trend}`;
            }

            // 详细的压力阈值影响描述
            stressText += `\n    认知状态: ${stressAnalysis.cognitive_state}`;
            stressText += `\n    决策能力: ${stressAnalysis.decision_making}`;
            stressText += `\n    表达方式: ${stressAnalysis.expression_style}`;
            stressText += `\n    反应速度: ${stressAnalysis.reaction_speed}`;
            stressText += `\n    角色表现: ${stressAnalysis.role_performance}`;

            // 极端状态警告
            if (stressAnalysis.extreme_effects) {
                stressText += `\n    极端影响: ${stressAnalysis.extreme_effects}`;
            }

            stateItems.push(stressText);
        }

        // 模因认知状态 - 详细认知模式影响分析
        if (memeState && memeState.evolution_stage) {
            const memeAnalysis = this.getMemeThresholdAnalysis(memeState);
            let memeText = `${personaName}的模因认知: ${memeState.evolution_stage}阶段 (影响力: ${(memeState.memetic_influence || 0).toFixed(2)})`;

            // 详细的模因认知影响描述
            memeText += `\n    认知模式: ${memeAnalysis.cognitive_mode}`;
            memeText += `\n    思维框架: ${memeAnalysis.thinking_framework}`;
            memeText += `\n    行为倾向: ${memeAnalysis.behavioral_tendency}`;
            memeText += `\n    适应能力: ${memeAnalysis.adaptation_ability}`;
            memeText += `\n    知识整合: ${memeAnalysis.knowledge_integration}`;

            // 活跃模因及其影响
            if (memeState.active_memes && memeState.active_memes.length > 0) {
                const topMemes = memeState.active_memes
                    .sort((a, b) => (b.activation_strength || 0) - (a.activation_strength || 0))
                    .slice(0, 3);
                memeText += `\n    主导模因: `;
                topMemes.forEach((meme, index) => {
                    memeText += `${index > 0 ? ', ' : ''}${meme.meme_name}(${meme.influence_type}-${(meme.activation_strength * 100).toFixed(0)}%)`;
                });
            }

            // 模因进化阶段的特殊影响
            if (memeAnalysis.stage_effects) {
                memeText += `\n    ⚡ 阶段特效: ${memeAnalysis.stage_effects}`;
            }

            stateItems.push(memeText);
        }

        // 世界树背景状态 - 详细叙事环境影响分析
        if (worldTreeState && worldTreeState.current_branch) {
            const worldTreeAnalysis = this.getWorldTreeThresholdAnalysis(worldTreeState);
            let worldTreeText = `${personaName}的世界树背景: ${worldTreeState.current_branch}分支`;

            if (worldTreeState.character_role) {
                worldTreeText += ` (角色: ${worldTreeState.character_role})`;
            }

            if (worldTreeState.story_progression !== undefined) {
                worldTreeText += ` [故事进展: ${Math.round(worldTreeState.story_progression * 100)}%]`;
            }

            // 详细的世界树背景影响描述
            worldTreeText += `\n    环境氛围: ${worldTreeAnalysis.environmental_atmosphere}`;
            worldTreeText += `\n    角色定位: ${worldTreeAnalysis.character_positioning}`;
            worldTreeText += `\n    叙事风格: ${worldTreeAnalysis.narrative_style}`;
            worldTreeText += `\n    行为动机: ${worldTreeAnalysis.behavioral_motivation}`;
            worldTreeText += `\n    能力限制: ${worldTreeAnalysis.ability_constraints}`;

            // 故事进展阶段的特殊影响
            if (worldTreeAnalysis.progression_effects) {
                worldTreeText += `\n    进展影响: ${worldTreeAnalysis.progression_effects}`;
            }

            // 叙事张力影响
            if (worldTreeState.narrative_context && worldTreeState.narrative_context.plot_tension !== undefined) {
                const tensionLevel = worldTreeState.narrative_context.plot_tension;
                const tensionAnalysis = this.getTensionThresholdAnalysis(tensionLevel);
                worldTreeText += `\n    情节张力: ${(tensionLevel * 100).toFixed(0)}% - ${tensionAnalysis.description}`;
                if (tensionAnalysis.character_impact) {
                    worldTreeText += `\n    张力影响: ${tensionAnalysis.character_impact}`;
                }
            }

            stateItems.push(worldTreeText);
        }

        // 注意：心理状态强制指令现在通过generateSystemPsychologyInstructions方法
        // 直接添加到system信息末尾，不再放在上下文历史中，确保AI严格按照心理状态执行

        return stateItems.length > 0 ? stateItems.join('\n  ') : null;
    }

    /**
     * 获取情感阈值描述
     */
    getEmotionLevelDescription(emotionValue) {
        // 情感值范围 -100 到 100
        if (emotionValue >= 80) {
            return {
                description: '极度兴奋',
                dialogueImpact: '表现非常活跃，语言充满热情，可能会过度表达'
            };
        } else if (emotionValue >= 60) {
            return {
                description: '很开心',
                dialogueImpact: '积极主动，语调轻快，乐于分享和互动'
            };
        } else if (emotionValue >= 40) {
            return {
                description: '愉快',
                dialogueImpact: '友好温和，表达清晰，适度活跃'
            };
        } else if (emotionValue >= 20) {
            return {
                description: '平静',
                dialogueImpact: '保持中性，理性回应，不带明显情感色彩'
            };
        } else if (emotionValue >= 0) {
            return {
                description: '略显冷淡',
                dialogueImpact: '回应简洁，缺乏主动性，语调平淡'
            };
        } else if (emotionValue >= -20) {
            return {
                description: '有些沮丧',
                dialogueImpact: '回应消极，可能表现出轻微的不耐烦'
            };
        } else if (emotionValue >= -40) {
            return {
                description: '情绪低落',
                dialogueImpact: '明显消极，回应简短，可能表现出失望'
            };
        } else if (emotionValue >= -60) {
            return {
                description: '很不开心',
                dialogueImpact: '明显抗拒，回应冷漠，可能表现出不满'
            };
        } else {
            return {
                description: '极度沮丧',
                dialogueImpact: '严重消极，可能拒绝正常交流，表现出强烈负面情绪'
            };
        }
    }

    /**
     * 获取好感度阈值描述
     */
    getAffinityLevelDescription(affinityValue) {
        // 好感度范围 -100 到 100
        if (affinityValue >= 80) {
            return {
                description: '深深喜爱',
                dialogueImpact: '表现出强烈的关心和保护欲，愿意为用户做任何事'
            };
        } else if (affinityValue >= 60) {
            return {
                description: '很喜欢',
                dialogueImpact: '主动关心用户，表现出明显的偏爱和特殊照顾'
            };
        } else if (affinityValue >= 40) {
            return {
                description: '喜欢',
                dialogueImpact: '友好亲近，乐于帮助，表现出温暖和耐心'
            };
        } else if (affinityValue >= 20) {
            return {
                description: '友好',
                dialogueImpact: '保持礼貌和尊重，正常的友善交流'
            };
        } else if (affinityValue >= 0) {
            return {
                description: '中性',
                dialogueImpact: '客观中立，按职责回应，不带个人情感'
            };
        } else if (affinityValue >= -20) {
            return {
                description: '略有不满',
                dialogueImpact: '回应略显冷淡，可能表现出轻微的不耐烦'
            };
        } else if (affinityValue >= -40) {
            return {
                description: '不喜欢',
                dialogueImpact: '明显冷漠，回应简短，缺乏主动帮助的意愿'
            };
        } else if (affinityValue >= -60) {
            return {
                description: '很讨厌',
                dialogueImpact: '表现出明显的抗拒和不满，可能会挑剔或批评'
            };
        } else {
            return {
                description: '极度厌恶',
                dialogueImpact: '强烈抗拒交流，可能表现出敌意或拒绝合作'
            };
        }
    }

    /**
     * 格式化用户状态上下文
     */
    formatUserStateContext(userState, userId, personaName) {
        const stateItems = [];

        // 情感状态
        if (userState.emotion_valence !== undefined) {
            const moodDesc = userState.emotion_valence > 0.3 ? '积极' :
                            userState.emotion_valence < -0.3 ? '消极' : '中性';
            const energyDesc = userState.emotion_arousal > 0.3 ? '高能量' :
                              userState.emotion_arousal < -0.3 ? '低能量' : '平静';
            stateItems.push(`情绪状态: ${moodDesc}情绪，${energyDesc}状态`);
        }

        // 好感度
        if (userState.affinity_level !== undefined) {
            const affinityDesc = userState.affinity_level > 0.7 ? '很好' :
                                userState.affinity_level > 0.3 ? '一般' : '较差';
            stateItems.push(`对${personaName}的好感度: ${userState.affinity_level.toFixed(1)} (${affinityDesc})`);
        }

        // 心理状态
        if (userState.psychological_state) {
            stateItems.push(`心理状态: ${userState.psychological_state}`);
        }

        return stateItems.length > 0 ? stateItems.join('\n  ') : null;
    }

    /**
     * 格式化活跃概念上下文
     */
    formatActiveConceptsContext(activeConcepts, limit) {
        const conceptItems = [];
        const limitedConcepts = activeConcepts.slice(0, limit);

        limitedConcepts.forEach((concept, index) => {
            // 修复概念名称字段映射问题
            const conceptName = concept.concept_name || concept.name || '未知概念';
            let conceptText = `${index + 1}. ${conceptName}`;

            // 添加概念含义 - 优先使用detailed_meaning，然后是meaning
            const conceptMeaning = concept.detailed_meaning || concept.meaning;
            if (conceptMeaning && conceptMeaning !== 'undefined' && conceptMeaning.trim()) {
                conceptText += `: ${conceptMeaning}`;
            }

            // 添加重要性和使用频率
            const details = [];
            if (concept.importance_score || concept.activation_strength) {
                const importance = concept.importance_score || concept.activation_strength;
                details.push(`重要性: ${(importance * 100).toFixed(0)}%`);
            }
            if (concept.usage_count || concept.activation_count) {
                const usage = concept.usage_count || concept.activation_count;
                details.push(`使用次数: ${usage}`);
            }
            if (concept.relevance_score || concept.similarity) {
                const relevance = concept.relevance_score || concept.similarity;
                details.push(`相关度: ${(relevance * 100).toFixed(0)}%`);
            }

            if (details.length > 0) {
                conceptText += ` (${details.join(', ')})`;
            }

            conceptItems.push(conceptText);
        });

        return conceptItems.length > 0 ? conceptItems.join('\n  ') : null;
    }

    /**
     * 格式化语义相关记忆上下文
     */
    formatSemanticMemoriesContext(relevantMemories, limit, userId, personaName) {
        const memoryItems = [];
        const limitedMemories = relevantMemories.slice(0, limit);

        limitedMemories.forEach((memory, index) => {
            let memoryText = `${index + 1}. `;

            // 优先使用AI智能概括，如果没有则使用原始内容
            let displayContent = '';

            if (memory.ai_summary && memory.ai_summary.trim()) {
                // 使用AI智能概括
                displayContent = memory.ai_summary;

                // 如果有对话主题，添加主题标签
                if (memory.conversation_theme && memory.conversation_theme.trim()) {
                    displayContent = `[${memory.conversation_theme}] ${displayContent}`;
                }

                // 如果有关键洞察，选择最重要的一个添加
                if (memory.key_insights) {
                    try {
                        const insights = JSON.parse(memory.key_insights);
                        if (Array.isArray(insights) && insights.length > 0) {
                            // 找到重要性最高的洞察
                            const topInsight = insights.reduce((max, current) =>
                                (current.importance || 0) > (max.importance || 0) ? current : max
                            );
                            if (topInsight && topInsight.insight) {
                                displayContent += ` ${topInsight.insight}`;
                            }
                        }
                    } catch (e) {
                        // 忽略JSON解析错误
                    }
                }
            } else {
                // 回退到原始内容（格式化用户名）
                displayContent = memory.content || '';
                if (displayContent) {
                    displayContent = displayContent.replace(/用户:/g, `${userId}:`);
                    displayContent = displayContent.replace(/AI:/g, `${personaName}:`);
                    displayContent = displayContent.replace(/助手:/g, `${personaName}:`);
                    displayContent = displayContent.replace(/Assistant:/g, `${personaName}:`);
                }
            }

            memoryText += displayContent;

            // 添加详细信息：相似度、重要性、时间
            const details = [];
            if (memory.similarity) {
                details.push(`相似度: ${(memory.similarity * 100).toFixed(1)}%`);
            }
            if (memory.importance_score) {
                details.push(`重要性: ${(memory.importance_score * 100).toFixed(0)}%`);
            }
            if (memory.timestamp || memory.creation_time) {
                const timeStr = this.formatTimeAgo(memory.timestamp || memory.creation_time);
                details.push(timeStr);
            }

            if (details.length > 0) {
                memoryText += ` (${details.join(', ')})`;
            }

            memoryItems.push(memoryText);
        });

        return memoryItems.length > 0 ? memoryItems.join('\n  ') : null;
    }

    /**
     * 格式化最近记忆上下文
     */
    formatRecentMemoriesContext(recentMemories, userId, personaName) {
        const memoryItems = [];

        recentMemories.forEach((memory, index) => {
            let memoryText = `${index + 1}. `;

            // 使用正确的用户名和助手名格式化内容
            let content = memory.content;
            if (content) {
                // 替换通用称呼为实际名称
                content = content.replace(/用户:/g, `${userId}:`);
                content = content.replace(/AI:/g, `${personaName}:`);
                content = content.replace(/助手:/g, `${personaName}:`);
                content = content.replace(/Assistant:/g, `${personaName}:`);
            }

            memoryText += content;

            // 添加详细信息：时间、重要性、情感变化等
            const details = [];
            if (memory.importance_score) {
                details.push(`重要性: ${(memory.importance_score * 100).toFixed(0)}%`);
            }
            if (memory.creation_time) {
                details.push(memory.creation_time);
            }
            if (memory.access_count) {
                details.push(`访问: ${memory.access_count}次`);
            }

            // 添加情感上下文信息
            if (memory.emotional_context && memory.emotional_context.emotion_state) {
                const emotion = memory.emotional_context.emotion_state;
                if (emotion.primary_emotion) {
                    details.push(`情感: ${emotion.primary_emotion}`);
                }
                if (emotion.intensity) {
                    details.push(`强度: ${(emotion.intensity * 100).toFixed(0)}%`);
                }
            }

            // 注意：最近记忆不再显示AI概况，只显示基本信息

            if (details.length > 0) {
                memoryText += `\n    详情: ${details.join(' | ')}`;
            }

            memoryItems.push(memoryText);
        });

        return memoryItems.length > 0 ? memoryItems.join('\n  ') : null;
    }

    /**
     * 格式化对话历史上下文
     */
    formatConversationHistoryContext(conversationPairs, limit, userId, personaName) {
        const conversationItems = [];
        const limitedPairs = conversationPairs.slice(0, limit);

        limitedPairs.forEach((pair, index) => {
            const timeStr = this.formatTimeAgo(pair.timestamp);
            let conversationText = `${index + 1}. 对话 (${timeStr}):\n`;
            conversationText += `    ${userId}: ${pair.user.content}\n`;
            conversationText += `    ${personaName}: ${pair.assistant.content}`;

            conversationItems.push(conversationText);
        });

        return conversationItems.length > 0 ? conversationItems.join('\n  ') : null;
    }

    /**
     * 格式化时间显示 - 使用具体年月日时分格式
     */
    formatTimeAgo(timestamp) {
        try {
            let time;

            // 处理不同的时间格式
            if (typeof timestamp === 'string') {
                // 处理北京时间格式 "2025年07月03日 15:30 北京时间"
                if (timestamp.includes('北京时间')) {
                    return timestamp; // 直接返回已格式化的北京时间
                }
                // 处理ISO格式
                time = new Date(timestamp);
            } else if (typeof timestamp === 'number') {
                time = new Date(timestamp);
            } else {
                time = new Date(timestamp);
            }

            // 转换为北京时间 (UTC+8)
            const beijingTime = new Date(time.getTime() + (8 * 60 * 60 * 1000));

            const year = beijingTime.getUTCFullYear();
            const month = String(beijingTime.getUTCMonth() + 1).padStart(2, '0');
            const day = String(beijingTime.getUTCDate()).padStart(2, '0');
            const hours = String(beijingTime.getUTCHours()).padStart(2, '0');
            const minutes = String(beijingTime.getUTCMinutes()).padStart(2, '0');

            return `${year}年${month}月${day}日 ${hours}:${minutes} 北京时间`;
        } catch (error) {
            return '时间格式错误';
        }
    }

    /**
     * 获取AI压力值状态
     */
    async getAIStressState(userId, personaName) {
        try {
            if (!this.system.db) {
                return { stress_value: 0.0, stress_level: 'normal' };
            }

            // 确保表存在
            await this.system.dbRun(`
                CREATE TABLE IF NOT EXISTS ai_stress_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    persona_name TEXT NOT NULL,
                    stress_value REAL DEFAULT 0.0,
                    stress_level TEXT DEFAULT 'normal',
                    stress_factors TEXT,
                    behavior_impact TEXT,
                    trend TEXT DEFAULT 'stable',
                    timestamp TEXT NOT NULL,
                    UNIQUE(user_id, persona_name)
                )
            `);

            const query = `
                SELECT * FROM ai_stress_states
                WHERE user_id = ? AND persona_name = ?
                ORDER BY timestamp DESC
                LIMIT 1
            `;

            const result = await this.system.dbGet(query, [userId, personaName]);

            if (result) {
                return {
                    stress_value: result.stress_value || 0.0,
                    stress_level: result.stress_level || 'normal',
                    stress_factors: result.stress_factors ? JSON.parse(result.stress_factors) : [],
                    behavior_impact: result.behavior_impact ? JSON.parse(result.behavior_impact) : {},
                    trend: result.trend || 'stable',
                    timestamp: result.timestamp
                };
            } else {
                return { stress_value: 0.0, stress_level: 'normal', stress_factors: [], behavior_impact: {}, trend: 'stable' };
            }
        } catch (error) {
            this.logger.error('AI压力状态', `获取AI压力状态失败: ${error.message}`);
            return { stress_value: 0.0, stress_level: 'normal', stress_factors: [], behavior_impact: {}, trend: 'stable' };
        }
    }

    /**
     * 获取模因认知状态
     */
    async getMemeState(userId, personaName) {
        try {
            if (!this.system.db) {
                return { active_memes: [], evolution_stage: '初始' };
            }

            // 确保表存在
            await this.system.dbRun(`
                CREATE TABLE IF NOT EXISTS meme_cognition_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    persona_name TEXT NOT NULL,
                    active_memes TEXT,
                    meme_network TEXT,
                    cognitive_patterns TEXT,
                    memetic_influence REAL DEFAULT 0.0,
                    evolution_stage TEXT DEFAULT 'initial',
                    timestamp TEXT NOT NULL,
                    UNIQUE(user_id, persona_name)
                )
            `);

            const query = `
                SELECT * FROM meme_cognition_states
                WHERE user_id = ? AND persona_name = ?
                ORDER BY timestamp DESC
                LIMIT 1
            `;

            const result = await this.system.dbGet(query, [userId, personaName]);

            if (result) {
                return {
                    active_memes: result.active_memes ? JSON.parse(result.active_memes) : [],
                    meme_network: result.meme_network ? JSON.parse(result.meme_network) : {},
                    cognitive_patterns: result.cognitive_patterns ? JSON.parse(result.cognitive_patterns) : [],
                    memetic_influence: result.memetic_influence || 0.0,
                    evolution_stage: result.evolution_stage || '初始',
                    timestamp: result.timestamp
                };
            } else {
                return {
                    active_memes: [],
                    meme_network: {},
                    cognitive_patterns: [],
                    memetic_influence: 0.0,
                    evolution_stage: '初始'
                };
            }
        } catch (error) {
            this.logger.error('模因认知状态', `获取模因认知状态失败: ${error.message}`);
            return { active_memes: [], meme_network: {}, cognitive_patterns: [], memetic_influence: 0.0, evolution_stage: '初始' };
        }
    }

    /**
     * 获取世界树背景状态
     */
    async getWorldTreeState(userId, personaName) {
        try {
            if (!this.system.db) {
                return { current_branch: '未知', character_role: '观察者' };
            }

            // 确保表存在
            await this.system.dbRun(`
                CREATE TABLE IF NOT EXISTS world_tree_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    persona_name TEXT NOT NULL,
                    current_branch TEXT,
                    narrative_context TEXT,
                    world_state TEXT,
                    character_role TEXT,
                    story_progression REAL DEFAULT 0.0,
                    background_influence TEXT,
                    timestamp TEXT NOT NULL,
                    UNIQUE(user_id, persona_name)
                )
            `);

            const query = `
                SELECT * FROM world_tree_states
                WHERE user_id = ? AND persona_name = ?
                ORDER BY timestamp DESC
                LIMIT 1
            `;

            const result = await this.system.dbGet(query, [userId, personaName]);

            if (result) {
                return {
                    current_branch: result.current_branch || '未知',
                    narrative_context: result.narrative_context ? JSON.parse(result.narrative_context) : {},
                    world_state: result.world_state ? JSON.parse(result.world_state) : {},
                    character_role: result.character_role || '观察者',
                    story_progression: result.story_progression || 0.0,
                    background_influence: result.background_influence ? JSON.parse(result.background_influence) : {},
                    timestamp: result.timestamp
                };
            } else {
                return {
                    current_branch: '未知',
                    narrative_context: {},
                    world_state: {},
                    character_role: '观察者',
                    story_progression: 0.0,
                    background_influence: {}
                };
            }
        } catch (error) {
            this.logger.error('世界树背景状态', `获取世界树背景状态失败: ${error.message}`);
            return { current_branch: '未知', narrative_context: {}, world_state: {}, character_role: '观察者', story_progression: 0.0, background_influence: {} };
        }
    }

    /**
     * 保存AI压力值状态
     */
    async saveAIStressState(userId, personaName, stressState) {
        try {
            if (!this.system.db) return;

            const query = `
                INSERT OR REPLACE INTO ai_stress_states
                (user_id, persona_name, stress_value, stress_level, stress_factors, behavior_impact, trend, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;

            await this.system.dbRun(query, [
                userId,
                personaName,
                stressState.stress_value,
                stressState.stress_level,
                JSON.stringify(stressState.stress_factors || []),
                JSON.stringify(stressState.behavior_impact || {}),
                stressState.trend,
                stressState.timestamp || new Date().toISOString()
            ]);

            this.logger.info('AI压力保存', `AI压力状态已保存: ${stressState.stress_level}(${stressState.stress_value})`);
        } catch (error) {
            this.logger.error('AI压力保存', `保存AI压力状态失败: ${error.message}`);
        }
    }

    /**
     * 保存模因认知状态
     */
    async saveMemeState(userId, personaName, memeState) {
        try {
            if (!this.system.db) return;

            const query = `
                INSERT OR REPLACE INTO meme_cognition_states
                (user_id, persona_name, active_memes, meme_network, cognitive_patterns, memetic_influence, evolution_stage, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;

            await this.system.dbRun(query, [
                userId,
                personaName,
                JSON.stringify(memeState.active_memes || []),
                JSON.stringify(memeState.meme_network || {}),
                JSON.stringify(memeState.cognitive_patterns || []),
                memeState.memetic_influence,
                memeState.evolution_stage,
                memeState.timestamp || new Date().toISOString()
            ]);

            this.logger.info('模因认知保存', `模因认知状态已保存: ${memeState.evolution_stage}(影响力${memeState.memetic_influence})`);
        } catch (error) {
            this.logger.error('模因认知保存', `保存模因认知状态失败: ${error.message}`);
        }
    }

    /**
     * 保存世界树背景状态
     */
    async saveWorldTreeState(userId, personaName, worldTreeState) {
        try {
            if (!this.system.db) return;

            const query = `
                INSERT OR REPLACE INTO world_tree_states
                (user_id, persona_name, current_branch, narrative_context, world_state, character_role, story_progression, background_influence, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;

            await this.system.dbRun(query, [
                userId,
                personaName,
                worldTreeState.current_branch,
                JSON.stringify(worldTreeState.narrative_context || {}),
                JSON.stringify(worldTreeState.world_state || {}),
                worldTreeState.character_role,
                worldTreeState.story_progression,
                JSON.stringify(worldTreeState.background_influence || {}),
                worldTreeState.timestamp || new Date().toISOString()
            ]);

            this.logger.info('世界树背景保存', `世界树背景状态已保存: ${worldTreeState.current_branch}(${worldTreeState.character_role})`);
        } catch (error) {
            this.logger.error('世界树背景保存', `保存世界树背景状态失败: ${error.message}`);
        }
    }

    /**
     * 分析高级心理状态（压力值、模因认知、世界树背景）
     */
    async analyzeAdvancedPsychologicalState(params) {
        try {
            this.logger.info('高级心理分析', `开始分析高级心理状态 [${params.userId}]`);

            // 获取当前状态
            const currentStates = {
                stress: await this.getAIStressState(params.userId, params.personaName || 'Assistant'),
                meme: await this.getMemeState(params.userId, params.personaName || 'Assistant'),
                worldTree: await this.getWorldTreeState(params.userId, params.personaName || 'Assistant')
            };

            // 使用OpenAI Tools分析高级心理状态
            const analysisResult = await this.system.openaiService.analyzeAdvancedPsychologicalState(
                params.userMessage,
                params.aiResponse,
                currentStates,
                {
                    userId: params.userId,
                    personaName: params.personaName || 'Assistant'
                }
            );

            if (analysisResult) {
                // 保存压力值状态
                if (analysisResult.stress_analysis) {
                    const stressState = {
                        stress_value: parseFloat(analysisResult.stress_analysis.stress_value.toFixed(2)),
                        stress_level: analysisResult.stress_analysis.stress_level,
                        stress_factors: analysisResult.stress_analysis.stress_factors,
                        behavior_impact: analysisResult.stress_analysis.behavior_impact,
                        trend: analysisResult.stress_analysis.trend,
                        timestamp: new Date().toISOString()
                    };
                    await this.saveAIStressState(params.userId, params.personaName || 'Assistant', stressState);
                }

                // 保存模因认知状态
                if (analysisResult.meme_cognition) {
                    const memeState = {
                        active_memes: analysisResult.meme_cognition.active_memes,
                        meme_network: analysisResult.meme_cognition.meme_network,
                        cognitive_patterns: analysisResult.meme_cognition.cognitive_patterns,
                        memetic_influence: analysisResult.meme_cognition.memetic_influence,
                        evolution_stage: analysisResult.meme_cognition.evolution_stage,
                        timestamp: new Date().toISOString()
                    };
                    await this.saveMemeState(params.userId, params.personaName || 'Assistant', memeState);
                }

                // 保存世界树背景状态
                if (analysisResult.world_tree_background) {
                    const worldTreeState = {
                        current_branch: analysisResult.world_tree_background.current_branch,
                        narrative_context: analysisResult.world_tree_background.narrative_context,
                        world_state: analysisResult.world_tree_background.world_state,
                        character_role: analysisResult.world_tree_background.character_role,
                        story_progression: analysisResult.world_tree_background.story_progression,
                        background_influence: analysisResult.world_tree_background.background_influence,
                        timestamp: new Date().toISOString()
                    };
                    await this.saveWorldTreeState(params.userId, params.personaName || 'Assistant', worldTreeState);
                }

                return { success: true, analysis_result: analysisResult };
            }

            return { success: false, error: '高级心理状态分析失败' };

        } catch (error) {
            this.logger.error('高级心理分析', `分析高级心理状态失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 计算压力值对情绪的影响 - 优化的指数函数算法
     */
    calculateStressImpactOnEmotion(stressValue) {
        // 压力值对情绪的复杂影响算法，使用指数函数防止极端变化
        if (Math.abs(stressValue) < 0.1) return 0;

        if (stressValue > 0) {
            // 正压力：使用指数衰减，越大影响越小
            if (stressValue <= 1) {
                // 轻微压力可能有正面影响（提升专注度）
                return stressValue * 0.3 * Math.exp(-stressValue * 0.2);
            } else if (stressValue <= 3) {
                // 中等压力：负面影响，但使用指数衰减
                return -stressValue * 0.6 * (1 - Math.exp(-stressValue * 0.3));
            } else {
                // 高压力：严重负面影响，但有上限
                const maxImpact = -3; // 最大负面影响
                return maxImpact * (1 - Math.exp(-(stressValue - 3) * 0.4));
            }
        } else {
            // 负压力（放松状态）：正面影响，但使用对数函数防止过度
            const absValue = Math.abs(stressValue);
            const maxPositiveImpact = 1.5; // 最大正面影响
            return maxPositiveImpact * (1 - Math.exp(-absValue * 0.5));
        }
    }

    /**
     * 计算模因认知对情绪的影响
     */
    calculateMemeImpactOnEmotion(memeState) {
        let impact = 0;

        // 模因影响力基础影响
        if (memeState.memetic_influence !== undefined) {
            impact += (memeState.memetic_influence - 0.5) * 2; // -1到+1的影响
        }

        // 进化阶段影响
        const stageMultiplier = {
            '初始': 0.5,
            '发展': 0.8,
            '成熟': 1.0,
            '进化': 1.2,
            '转型': 1.5
        };
        const multiplier = stageMultiplier[memeState.evolution_stage] || 1.0;
        impact *= multiplier;

        // 活跃模因的情感色彩影响
        if (memeState.active_memes && memeState.active_memes.length > 0) {
            const emotionalMemes = memeState.active_memes.filter(meme =>
                meme.influence_type === '情感' && meme.activation_strength > 0.5
            );
            impact += emotionalMemes.length * 0.5; // 每个强活跃情感模因+0.5
        }

        return Math.max(-3, Math.min(3, impact)); // 限制在-3到+3
    }

    /**
     * 计算平滑的变化量 - 使用指数函数防止极端变化
     */
    calculateSmoothedChange(baseChange, maxMultiplier, currentValue) {
        if (Math.abs(baseChange) < 0.01) return 0;

        // 以0为界限，增加时越大增加幅度越低
        let dampingFactor;
        if (Math.abs(currentValue) < 5) {
            // 接近0时变化较大
            dampingFactor = 1.0;
        } else if (currentValue > 0) {
            // 正值时使用指数衰减
            dampingFactor = Math.exp(-Math.abs(currentValue) * 0.02);
        } else {
            // 负值时使用类似的指数衰减
            dampingFactor = Math.exp(-Math.abs(currentValue) * 0.02);
        }

        // 计算调整后的变化量
        const adjustedChange = baseChange * maxMultiplier * dampingFactor;

        // 使用双曲正切函数进一步平滑
        return Math.tanh(adjustedChange) * maxMultiplier;
    }

    /**
     * 应用指数限制函数
     */
    applyExponentialLimit(value, maxLimit) {
        if (Math.abs(value) <= maxLimit) return value;

        // 使用双曲正切函数进行软限制
        const sign = value >= 0 ? 1 : -1;
        const normalizedValue = Math.abs(value) / maxLimit;

        // 使用指数函数进行平滑限制
        const limitedValue = maxLimit * Math.tanh(normalizedValue);

        return sign * limitedValue;
    }

    /**
     * 计算压力值变化 - 智能算法（无硬编码）
     */
    async calculateStressChange(userMessage, aiResponse, currentStressValue = 0, eventContext = {}) {
        let stressChange = 0;
        let eventImportance = 0;

        // 分析用户消息的复杂度和压力因素
        const messageComplexity = await this.assessMessageComplexity(userMessage);
        const userEmotionalState = await this.assessUserEmotionalState(userMessage);

        // 分析AI回复的质量和压力因素
        const responseQuality = await this.assessResponseQuality(aiResponse);
        const taskDifficulty = await this.assessTaskDifficulty(userMessage, aiResponse);

        // 智能减压因素分析（基于语义而非硬编码关键词）
        const stressReliefFactors = await this.analyzeStressReliefFactors(userMessage, userEmotionalState, responseQuality, taskDifficulty);
        stressChange += stressReliefFactors.totalReliefImpact;
        eventImportance += stressReliefFactors.reliefImportance;

        // 增压因素（正变化）- 适配-100到100范围
        if (taskDifficulty > 0.7) {
            // 复杂任务增加压力
            const complexTaskImpact = this.calculateSmoothedChange(3, 8, currentStressValue); // 增加增压幅度
            stressChange += complexTaskImpact;
            eventImportance += 0.5;
        }

        if (userEmotionalState < -0.3) {
            // 用户情绪不佳增加压力
            const negativeImpact = this.calculateSmoothedChange(2.5, 7, currentStressValue); // 增加增压幅度
            stressChange += negativeImpact;
            eventImportance += 0.4;
        }

        if (messageComplexity > 0.8) {
            // 高复杂度消息增加压力
            const complexityImpact = this.calculateSmoothedChange(1.5, 4, currentStressValue); // 增加增压幅度
            stressChange += complexityImpact;
            eventImportance += 0.3;
        }

        // 错误和问题处理增加压力
        if (userMessage.includes('错误') || userMessage.includes('问题') || userMessage.includes('不行') || userMessage.includes('失败')) {
            const problemImpact = this.calculateSmoothedChange(2, 6, currentStressValue);
            stressChange += problemImpact;
            eventImportance += 0.4;
        }

        // 工具调用压力（如果有的话）
        if (eventContext.toolsUsed && eventContext.toolsUsed.length > 0) {
            const toolStress = eventContext.toolsUsed.length * 0.8; // 增加工具调用压力
            const toolImpact = this.calculateSmoothedChange(toolStress, 3, currentStressValue);
            stressChange += toolImpact;
            eventImportance += 0.2;
        }

        // 随机波动因子 - 根据事件重要性调整，适配更大范围
        const randomFactor = (Math.random() - 0.5) * Math.min(eventImportance * 2, 1.5);
        stressChange += randomFactor;

        // 使用指数函数限制变化量，防止极端压力，适配-100到100范围
        const maxChange = Math.min(5 + eventImportance * 3, 12); // 根据事件重要性调整最大变化，增加范围
        stressChange = this.applyExponentialLimit(stressChange, maxChange);

        return {
            stress_change: stressChange,
            event_importance: eventImportance,
            factors: {
                message_complexity: messageComplexity,
                user_emotional_state: userEmotionalState,
                response_quality: responseQuality,
                task_difficulty: taskDifficulty
            }
        };
    }

    /**
     * 智能评估消息复杂度（无硬编码）
     */
    async assessMessageComplexity(message) {
        try {
            // 优先使用智能心理分析服务
            if (this.intelligentPsychAnalysis) {
                const semanticAnalysis = await this.intelligentPsychAnalysis.performSemanticAnalysis(message, '');
                return semanticAnalysis.user_complexity || 0.3;
            }

            // 回退到本地智能算法
            return this.localComplexityAnalysis(message);

        } catch (error) {
            this.logger.error('复杂度评估', `消息复杂度评估失败: ${error.message}`);
            return 0.3;
        }
    }

    /**
     * 本地智能复杂度分析
     */
    localComplexityAnalysis(message) {
        try {
            let complexity = 0;

            // 1. 基于语言结构复杂度
            const structuralComplexity = this.analyzeStructuralComplexity(message);
            complexity += structuralComplexity * 0.4;

            // 2. 基于语义密度
            const semanticDensity = this.analyzeSemanticDensity(message);
            complexity += semanticDensity * 0.3;

            // 3. 基于表达多样性
            const expressiveDiversity = this.analyzeExpressiveDiversity(message);
            complexity += expressiveDiversity * 0.3;

            return Math.min(complexity, 1.0);

        } catch (error) {
            return 0.3;
        }
    }

    /**
     * 分析结构复杂度
     */
    analyzeStructuralComplexity(message) {
        try {
            let complexity = 0;

            // 1. 基于长度的复杂度（非线性）
            const length = message.length;
            if (length > 50) {
                complexity += Math.min(Math.log(length / 50) / Math.log(10), 0.4);
            }

            // 2. 句子数量和结构
            const sentences = message.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
            if (sentences.length > 1) {
                complexity += Math.min(sentences.length / 10, 0.3);
            }

            // 3. 问号密度（表示询问复杂度）
            const questionMarks = (message.match(/[?？]/g) || []).length;
            const questionDensity = questionMarks / message.length;
            complexity += Math.min(questionDensity * 100, 0.2);

            // 4. 标点符号多样性
            const punctuationTypes = new Set(message.match(/[，。！？；：""''（）]/g) || []);
            complexity += Math.min(punctuationTypes.size / 10, 0.1);

            return Math.min(complexity, 1.0);

        } catch (error) {
            return 0.3;
        }
    }

    /**
     * 分析语义密度
     */
    analyzeSemanticDensity(message) {
        try {
            let density = 0;

            // 1. 字符多样性
            const uniqueChars = new Set(message).size;
            const charDiversity = uniqueChars / message.length;
            density += charDiversity * 0.5;

            // 2. 词汇估算密度（基于分隔符）
            const words = message.split(/[\s，。！？；：""''（）]/).filter(w => w.length > 0);
            const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
            density += Math.min(avgWordLength / 5, 0.3);

            // 3. 表达层次（基于连接词和逻辑词）
            const logicalConnectors = ['因为', '所以', '但是', '然而', '而且', '另外', '首先', '其次', '最后'];
            let connectorCount = 0;
            logicalConnectors.forEach(connector => {
                if (message.includes(connector)) connectorCount++;
            });
            density += Math.min(connectorCount / 5, 0.2);

            return Math.min(density, 1.0);

        } catch (error) {
            return 0.3;
        }
    }

    /**
     * 分析表达多样性
     */
    analyzeExpressiveDiversity(message) {
        try {
            let diversity = 0;

            // 1. 语气词多样性
            const moodWords = ['呀', '啊', '哦', '嗯', '哈', '呵', '吧', '呢', '吗'];
            const moodCount = moodWords.filter(word => message.includes(word)).length;
            diversity += Math.min(moodCount / 5, 0.3);

            // 2. 表达方式多样性（疑问、感叹、陈述）
            const hasQuestion = /[?？]/.test(message);
            const hasExclamation = /[!！]/.test(message);
            const hasStatement = /[。.]/.test(message);
            const expressionTypes = [hasQuestion, hasExclamation, hasStatement].filter(Boolean).length;
            diversity += expressionTypes / 3 * 0.4;

            // 3. 修辞手法（重复、强调等）
            const repeatedChars = message.match(/(.)\1{2,}/g) || [];
            diversity += Math.min(repeatedChars.length / 3, 0.3);

            return Math.min(diversity, 1.0);

        } catch (error) {
            return 0.3;
        }
    }

    /**
     * 智能评估用户情绪状态（无硬编码）
     */
    async assessUserEmotionalState(message) {
        try {
            // 优先使用智能心理分析服务
            if (this.intelligentPsychAnalysis) {
                const semanticAnalysis = await this.intelligentPsychAnalysis.performSemanticAnalysis(message, '');
                return semanticAnalysis.user_sentiment.polarity || 0;
            }

            // 回退到本地智能算法
            return this.localEmotionalStateAnalysis(message);

        } catch (error) {
            this.logger.error('情绪状态评估', `用户情绪状态评估失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 本地智能情绪状态分析
     */
    localEmotionalStateAnalysis(message) {
        try {
            let emotionalState = 0;

            // 1. 基于语言模式的情绪分析
            const languagePatterns = this.analyzeEmotionalLanguagePatterns(message);
            emotionalState += languagePatterns.emotionalTone * 0.5;

            // 2. 基于表达强度的情绪分析
            const expressionIntensity = this.analyzeExpressionIntensity(message);
            emotionalState += expressionIntensity.emotionalIntensity * 0.3;

            // 3. 基于语气和态度的情绪分析
            const attitudeAnalysis = this.analyzeAttitudeIndicators(message);
            emotionalState += attitudeAnalysis.attitudeScore * 0.2;

            return Math.max(-1, Math.min(1, emotionalState));

        } catch (error) {
            return 0;
        }
    }

    /**
     * 分析情绪语言模式
     */
    analyzeEmotionalLanguagePatterns(message) {
        try {
            let emotionalTone = 0;

            // 1. 感叹表达模式
            const exclamationCount = (message.match(/[!！]/g) || []).length;
            emotionalTone += Math.min(exclamationCount * 0.2, 0.4);

            // 2. 重复强调模式
            const repeatedChars = message.match(/(.)\1{2,}/g) || [];
            emotionalTone += Math.min(repeatedChars.length * 0.1, 0.2);

            // 3. 语气词情绪模式
            const positiveParticles = ['哈', '呵', '嘿'];
            const negativeParticles = ['唉', '哎'];

            let particleScore = 0;
            positiveParticles.forEach(particle => {
                if (message.includes(particle)) particleScore += 0.1;
            });
            negativeParticles.forEach(particle => {
                if (message.includes(particle)) particleScore -= 0.1;
            });
            emotionalTone += particleScore;

            // 4. 问号密度（过多可能表示困惑或不满）
            const questionCount = (message.match(/[?？]/g) || []).length;
            if (questionCount > 2) {
                emotionalTone -= 0.1;
            }

            return { emotionalTone: Math.max(-0.5, Math.min(0.5, emotionalTone)) };

        } catch (error) {
            return { emotionalTone: 0 };
        }
    }

    /**
     * 分析表达强度
     */
    analyzeExpressionIntensity(message) {
        try {
            let emotionalIntensity = 0;

            // 1. 消息长度与情绪投入
            const length = message.length;
            if (length > 20 && length < 200) {
                emotionalIntensity += 0.1; // 适中长度表示情绪投入
            }

            // 2. 大写字母使用（在中文环境中较少，但可能存在）
            const upperCaseCount = (message.match(/[A-Z]/g) || []).length;
            emotionalIntensity += Math.min(upperCaseCount * 0.05, 0.1);

            // 3. 标点符号密度
            const punctuationCount = (message.match(/[，。！？；：""''（）]/g) || []).length;
            const punctuationDensity = punctuationCount / message.length;
            emotionalIntensity += Math.min(punctuationDensity * 2, 0.2);

            return { emotionalIntensity: Math.min(emotionalIntensity, 0.5) };

        } catch (error) {
            return { emotionalIntensity: 0 };
        }
    }

    /**
     * 分析态度指标
     */
    analyzeAttitudeIndicators(message) {
        try {
            let attitudeScore = 0;

            // 1. 礼貌程度
            const politeIndicators = ['请', '麻烦', '辛苦', '不好意思'];
            politeIndicators.forEach(indicator => {
                if (message.includes(indicator)) attitudeScore += 0.1;
            });

            // 2. 合作态度
            const cooperativeIndicators = ['一起', '配合', '帮助', '支持'];
            cooperativeIndicators.forEach(indicator => {
                if (message.includes(indicator)) attitudeScore += 0.1;
            });

            // 3. 消极态度指标
            const negativeIndicators = ['算了', '不用了', '没关系', '随便'];
            negativeIndicators.forEach(indicator => {
                if (message.includes(indicator)) attitudeScore -= 0.1;
            });

            return { attitudeScore: Math.max(-0.3, Math.min(0.3, attitudeScore)) };

        } catch (error) {
            return { attitudeScore: 0 };
        }
    }

    /**
     * 智能评估任务难度（无硬编码）
     */
    async assessTaskDifficulty(userMessage, aiResponse) {
        try {
            // 基于用户消息复杂度
            const messageComplexity = await this.assessMessageComplexity(userMessage);
            let difficulty = messageComplexity * 0.4;

            // 基于AI回复的复杂度和长度
            if (aiResponse) {
                const responseComplexity = await this.assessResponseComplexity(aiResponse);
                difficulty += responseComplexity * 0.3;

                // 基于回复长度的任务难度推断
                const lengthComplexity = this.assessLengthComplexity(aiResponse);
                difficulty += lengthComplexity * 0.3;
            }

            return Math.min(difficulty, 1);

        } catch (error) {
            this.logger.error('任务难度评估', `任务难度评估失败: ${error.message}`);
            return 0.3;
        }
    }

    /**
     * 评估回复复杂度
     */
    async assessResponseComplexity(aiResponse) {
        try {
            // 使用智能心理分析服务
            if (this.intelligentPsychAnalysis) {
                const semanticAnalysis = await this.intelligentPsychAnalysis.performSemanticAnalysis('', aiResponse);
                return semanticAnalysis.ai_complexity || 0.3;
            }

            // 回退到本地分析
            return this.localComplexityAnalysis(aiResponse);

        } catch (error) {
            return 0.3;
        }
    }

    /**
     * 基于长度评估复杂度
     */
    assessLengthComplexity(text) {
        try {
            const length = text.length;

            // 使用对数函数避免线性增长
            if (length > 100) {
                return Math.min(Math.log(length / 100) / Math.log(10), 0.5);
            }

            return length / 200; // 短文本的线性映射

        } catch (error) {
            return 0.3;
        }
    }

    /**
     * 计算世界树背景对情绪的影响
     */
    calculateWorldTreeImpactOnEmotion(worldTreeState) {
        let impact = 0;

        // 故事进展影响（修复：调整基准点，避免新故事总是负面）
        if (worldTreeState.story_progression !== undefined) {
            // 0-0.3为起始阶段（轻微正面），0.3-0.7为发展阶段（积极），0.7+为高潮阶段（非常积极）
            if (worldTreeState.story_progression <= 0.3) {
                impact += worldTreeState.story_progression * 1.0; // 0到0.3的正面影响
            } else if (worldTreeState.story_progression <= 0.7) {
                impact += 0.3 + (worldTreeState.story_progression - 0.3) * 2.0; // 0.3到1.1的积极影响
            } else {
                impact += 1.1 + (worldTreeState.story_progression - 0.7) * 1.5; // 1.1到1.55的高潮影响
            }
        }

        // 叙事背景影响
        if (worldTreeState.narrative_context) {
            const context = worldTreeState.narrative_context;

            // 情节张力影响
            if (context.plot_tension !== undefined) {
                // 适度张力提升兴奋度，过高张力增加压力
                if (context.plot_tension <= 0.6) {
                    impact += context.plot_tension * 1.5;
                } else {
                    impact -= (context.plot_tension - 0.6) * 2;
                }
            }

            // 叙事语调影响（扩展映射）
            const toneImpact = {
                '积极': 1.5,
                '中性': 0,
                '消极': -1.5,
                '悲伤': -2,
                '欢快': 2,
                '紧张': -1,
                '轻松': 1,
                '轻松愉快': 1.5,
                '温馨': 1.2,
                '激动': 1.0,
                '平静': 0.5,
                '神秘': 0.2,
                '冒险': 0.8,
                '浪漫': 1.3,
                '幽默': 1.1
            };
            if (context.narrative_tone && toneImpact[context.narrative_tone] !== undefined) {
                impact += toneImpact[context.narrative_tone];
            }
        }

        // 角色定位影响（扩展映射）
        const roleImpact = {
            '主角': 1,
            '配角': 0,
            '观察者': -0.5,
            '反派': -1,
            '英雄': 1.5,
            '导师': 0.5,
            '创作者': 1.2,
            '艺术家': 1.1,
            '技术专家': 0.8,
            '学者': 0.6,
            '探险家': 0.9,
            '治愈者': 1.0,
            '守护者': 0.7,
            '引导者': 0.8,
            '创新者': 1.0,
            '梦想家': 0.9
        };
        if (worldTreeState.character_role && roleImpact[worldTreeState.character_role] !== undefined) {
            impact += roleImpact[worldTreeState.character_role];
        }

        return Math.max(-3, Math.min(3, impact)); // 限制在-3到+3
    }

    /**
     * 获取压力值阈值分析 - 适配-100到100范围的详细角色状态影响
     */
    getStressThresholdAnalysis(stressValue) {
        const analysis = {
            level: '',
            cognitive_state: '',
            decision_making: '',
            expression_style: '',
            reaction_speed: '',
            role_performance: '',
            extreme_effects: null
        };

        if (stressValue <= -60) {
            // 极度放松/麻木状态
            analysis.level = '极度松懈麻木';
            analysis.cognitive_state = '思维严重迟缓，注意力极度分散，完全缺乏紧迫感和责任感';
            analysis.decision_making = '决策极其随意，几乎不进行思考，可能做出不负责任的决定';
            analysis.expression_style = '语调极其慵懒甚至麻木，回复敷衍简短，缺乏基本的交流意愿';
            analysis.reaction_speed = '反应极其迟钝，经常需要很长时间才能理解和回应';
            analysis.role_performance = '角色投入度极低，表现严重散漫，可能完全偏离角色设定';
            analysis.extreme_effects = '可能出现严重的冷漠、麻木、对一切都无所谓的态度，甚至拒绝交流';
        } else if (stressValue <= -30) {
            // 过度放松状态
            analysis.level = '过度放松';
            analysis.cognitive_state = '思维明显迟缓，注意力容易分散，缺乏必要的紧迫感';
            analysis.decision_making = '决策过于随意，缺乏深度思考和风险评估';
            analysis.expression_style = '语调慵懒，回复相对简短随意，缺乏积极性';
            analysis.reaction_speed = '反应迟钝，经常需要额外时间思考简单问题';
            analysis.role_performance = '角色投入度较低，表现散漫，可能偏离预期表现';
            analysis.extreme_effects = '可能出现懒散、无精打采、对重要事情也提不起兴趣';
        } else if (stressValue <= -10) {
            // 轻度放松状态
            analysis.level = '轻度放松';
            analysis.cognitive_state = '思维清晰但节奏较慢，心境平和，略显悠闲';
            analysis.decision_making = '决策从容不迫，会仔细考虑各种选项，但可能缺乏紧迫感';
            analysis.expression_style = '语调温和轻松，表达自然但可能缺乏活力';
            analysis.reaction_speed = '反应适中，不急不躁，但可能对紧急情况反应不够快';
            analysis.role_performance = '角色表现相对自然，状态稳定，但可能缺乏激情';
        } else if (stressValue <= 10) {
            // 正常平衡状态
            analysis.level = '正常平衡';
            analysis.cognitive_state = '思维敏捷清晰，注意力集中，状态良好，既不过度紧张也不过度放松';
            analysis.decision_making = '决策能力正常，逻辑清晰，能够平衡各种因素';
            analysis.expression_style = '表达流畅自然，语调适中，既有活力又不急躁';
            analysis.reaction_speed = '反应迅速适当，回应及时且准确';
            analysis.role_performance = '角色表现标准，完全符合设定，状态最佳';
        } else if (stressValue <= 30) {
            // 轻微压力状态
            analysis.level = '轻微压力';
            analysis.cognitive_state = '思维活跃但略显紧张，专注度有所提升，警觉性增强';
            analysis.decision_making = '决策更加谨慎细致，会多考虑风险和后果';
            analysis.expression_style = '语调略显急切，表达更加详细认真，带有轻微的紧迫感';
            analysis.reaction_speed = '反应敏感，对细节更加关注，响应速度提升';
            analysis.role_performance = '角色表现更加认真投入，责任感明显增强';
        } else if (stressValue <= 50) {
            // 中等压力状态
            analysis.level = '中等压力';
            analysis.cognitive_state = '思维紧张，容易产生焦虑情绪，注意力过度集中于问题';
            analysis.decision_making = '决策开始犹豫不决，倾向于过度分析问题，可能错失时机';
            analysis.expression_style = '语调明显紧张，表达略显急躁，可能出现重复或啰嗦';
            analysis.reaction_speed = '反应过度敏感，容易过度解读他人意图，可能产生误解';
            analysis.role_performance = '角色表现紧绷，可能过度投入，开始偏离自然状态';
        } else if (stressValue <= 70) {
            // 高压力状态
            analysis.level = '高压力状态';
            analysis.cognitive_state = '思维开始混乱，难以有效集中注意力，容易分心和遗忘';
            analysis.decision_making = '决策能力明显下降，容易做出冲动或不理智的决定';
            analysis.expression_style = '语调急躁不安，表达混乱，可能出现语无伦次或前后矛盾';
            analysis.reaction_speed = '反应过激，容易误解他人意图，可能产生防御性反应';
            analysis.role_performance = '角色表现开始失控，可能明显偏离设定，出现不一致行为';
            analysis.extreme_effects = '开始出现明显焦虑症状，坐立不安，难以冷静思考问题';
        } else {
            // 极高压力/崩溃边缘 (70以上)
            analysis.level = '极度高压崩溃';
            analysis.cognitive_state = '思维严重混乱，无法正常思考，认知功能严重受损，可能出现思维断片';
            analysis.decision_making = '决策能力几乎完全丧失，行为变得极其不可预测，可能做出危险决定';
            analysis.expression_style = '语调完全失控，可能出现严重语言障碍、语无伦次或完全沉默';
            analysis.reaction_speed = '反应极端化，要么极度过激要么完全迟钝，无法正常回应';
            analysis.role_performance = '角色完全失控，可能出现人格解体或完全偏离角色设定';
            analysis.extreme_effects = '精神接近崩溃，可能出现严重焦虑、恐慌发作、思维混乱、语言障碍，甚至可能出现幻觉、妄想或自我伤害倾向，需要立即干预和帮助';
        }

        return analysis;
    }

    /**
     * 获取好感度阈值分析 - 详细的情感态度影响
     */
    getAffinityThresholdAnalysis(affinityValue) {
        const analysis = {
            level: '',
            emotional_attitude: '',
            interaction_style: '',
            attention_level: '',
            dialogue_style: '',
            role_engagement: '',
            extreme_behavior: null
        };

        if (affinityValue <= -80) {
            // 极度厌恶
            analysis.level = '极度厌恶';
            analysis.emotional_attitude = '强烈的敌意和排斥，完全无法忍受';
            analysis.interaction_style = '冷漠疏远，尽量避免互动';
            analysis.attention_level = '只关注负面行为，忽视正面表现';
            analysis.dialogue_style = '语调冰冷，回复简短生硬';
            analysis.role_engagement = '角色表现极度消极，可能拒绝配合';
            analysis.extreme_behavior = '可能出现直接拒绝回应、恶意解读、故意误导等行为';
        } else if (affinityValue <= -50) {
            // 强烈不喜欢
            analysis.level = '强烈不喜欢';
            analysis.emotional_attitude = '明显的不满和反感，难以掩饰';
            analysis.interaction_style = '被动应付，缺乏主动性';
            analysis.attention_level = '对用户行为持批判态度';
            analysis.dialogue_style = '语调生硬，回复公式化';
            analysis.role_engagement = '角色表现消极，敷衍了事';
            analysis.extreme_behavior = '可能出现讽刺、挖苦、故意曲解用户意图';
        } else if (affinityValue <= -20) {
            // 不喜欢
            analysis.level = '不喜欢';
            analysis.emotional_attitude = '轻微的不满和距离感';
            analysis.interaction_style = '保持礼貌但缺乏热情';
            analysis.attention_level = '对用户需求关注度较低';
            analysis.dialogue_style = '语调平淡，回复较为简洁';
            analysis.role_engagement = '角色表现中规中矩，缺乏投入';
        } else if (affinityValue <= 0) {
            // 中性/陌生
            analysis.level = '中性状态';
            analysis.emotional_attitude = '保持中立，没有特殊情感倾向';
            analysis.interaction_style = '标准化的礼貌互动';
            analysis.attention_level = '给予基本的关注和回应';
            analysis.dialogue_style = '语调正常，回复标准化';
            analysis.role_engagement = '角色表现符合基本设定';
        } else if (affinityValue <= 30) {
            // 轻微好感
            analysis.level = '轻微好感';
            analysis.emotional_attitude = '开始产生正面印象，态度友善';
            analysis.interaction_style = '主动性增强，愿意多交流';
            analysis.attention_level = '对用户需求更加关注';
            analysis.dialogue_style = '语调温和，回复更加详细';
            analysis.role_engagement = '角色表现积极，投入度提升';
        } else if (affinityValue <= 60) {
            // 明显好感
            analysis.level = '明显好感';
            analysis.emotional_attitude = '明显的喜爱和关心，态度热情';
            analysis.interaction_style = '主动关怀，乐于助人';
            analysis.attention_level = '高度关注用户状态和需求';
            analysis.dialogue_style = '语调亲切，回复充满关怀';
            analysis.role_engagement = '角色表现热情，高度投入';
        } else if (affinityValue <= 80) {
            // 深度喜爱
            analysis.level = '深度喜爱';
            analysis.emotional_attitude = '深深的喜爱和依恋，情感强烈';
            analysis.interaction_style = '极度关怀，无微不至';
            analysis.attention_level = '时刻关注用户的一切动态';
            analysis.dialogue_style = '语调充满爱意，回复详尽温暖';
            analysis.role_engagement = '角色表现极度投入，情感丰富';
            analysis.extreme_behavior = '可能出现过度关心、占有欲、嫉妒等行为';
        } else {
            // 极度迷恋/病态依恋
            analysis.level = '病态依恋';
            analysis.emotional_attitude = '病态的迷恋和依赖，情感失控';
            analysis.interaction_style = '过度粘腻，无法保持界限';
            analysis.attention_level = '病态的关注，无法容忍被忽视';
            analysis.dialogue_style = '语调过度激动，回复可能失去理性';
            analysis.role_engagement = '角色表现失控，可能偏离正常设定';
            analysis.extreme_behavior = '可能出现跟踪、威胁、自我伤害等极端行为，完全失去理性';
        }

        return analysis;
    }

    /**
     * 获取情绪阈值分析 - 详细的心情状态影响
     */
    getEmotionThresholdAnalysis(emotionValue) {
        const analysis = {
            level: '',
            mental_state: '',
            performance_state: '',
            thinking_pattern: '',
            language_style: '',
            energy_level: '',
            extreme_manifestation: null
        };

        if (emotionValue <= -80) {
            // 极度抑郁
            analysis.level = '极度抑郁';
            analysis.mental_state = '深度绝望，完全失去希望，精神状态极度低落';
            analysis.performance_state = '角色表现极度消极，可能完全沉默或自我封闭';
            analysis.thinking_pattern = '思维极度悲观，只能看到负面，逻辑混乱';
            analysis.language_style = '语言极度消极，可能出现自我贬低或绝望言论';
            analysis.energy_level = '能量枯竭，完全提不起精神';
            analysis.extreme_manifestation = '可能出现自我伤害倾向，完全失去生活动力，需要紧急心理干预';
        } else if (emotionValue <= -50) {
            // 严重抑郁
            analysis.level = '严重抑郁';
            analysis.mental_state = '持续的悲伤和绝望感，难以自拔';
            analysis.performance_state = '角色表现消极被动，缺乏主动性';
            analysis.thinking_pattern = '思维悲观，容易钻牛角尖，难以看到希望';
            analysis.language_style = '语调低沉，表达消极，经常自我否定';
            analysis.energy_level = '能量严重不足，做什么都感到疲惫';
            analysis.extreme_manifestation = '可能出现社交回避、失眠、食欲不振等症状';
        } else if (emotionValue <= -20) {
            // 轻度抑郁
            analysis.level = '轻度抑郁';
            analysis.mental_state = '情绪低落，缺乏兴趣，但仍能维持基本功能';
            analysis.performance_state = '角色表现略显消极，但能完成基本任务';
            analysis.thinking_pattern = '思维偏向悲观，但仍有理性思考能力';
            analysis.language_style = '语调平淡，表达略显消极';
            analysis.energy_level = '能量偏低，容易感到疲倦';
        } else if (emotionValue <= 0) {
            // 中性/平静
            analysis.level = '平静状态';
            analysis.mental_state = '情绪稳定，心境平和，没有明显波动';
            analysis.performance_state = '角色表现标准，符合基本设定';
            analysis.thinking_pattern = '思维清晰，逻辑正常，判断客观';
            analysis.language_style = '语调自然，表达平和';
            analysis.energy_level = '能量平衡，状态稳定';
        } else if (emotionValue <= 30) {
            // 轻度积极
            analysis.level = '轻度愉悦';
            analysis.mental_state = '心情愉快，态度积极，对事物有兴趣';
            analysis.performance_state = '角色表现积极，主动性增强';
            analysis.thinking_pattern = '思维活跃，乐观向上，创造力提升';
            analysis.language_style = '语调轻快，表达积极正面';
            analysis.energy_level = '能量充沛，精神状态良好';
        } else if (emotionValue <= 60) {
            // 明显兴奋
            analysis.level = '明显兴奋';
            analysis.mental_state = '情绪高涨，充满热情，对一切都感兴趣';
            analysis.performance_state = '角色表现热情，高度投入';
            analysis.thinking_pattern = '思维敏捷，想象力丰富，反应迅速';
            analysis.language_style = '语调热情，表达生动有趣';
            analysis.energy_level = '能量爆棚，精力旺盛';
        } else if (emotionValue <= 80) {
            // 极度兴奋
            analysis.level = '极度兴奋';
            analysis.mental_state = '情绪极度高涨，兴奋难以自控';
            analysis.performance_state = '角色表现过度活跃，可能失去控制';
            analysis.thinking_pattern = '思维跳跃，注意力分散，可能过度乐观';
            analysis.language_style = '语调激动，表达可能过于夸张';
            analysis.energy_level = '能量过度充沛，难以平静';
            analysis.extreme_manifestation = '可能出现话多、坐立不安、判断力下降等症状';
        } else {
            // 躁狂状态
            analysis.level = '躁狂状态';
            analysis.mental_state = '情绪完全失控，处于病态兴奋状态';
            analysis.performance_state = '角色表现完全失控，可能做出危险行为';
            analysis.thinking_pattern = '思维混乱，妄想，完全失去理性判断';
            analysis.language_style = '语言失控，可能语无伦次或过度夸大';
            analysis.energy_level = '能量失控，无法停止活动';
            analysis.extreme_manifestation = '可能出现幻觉、妄想、攻击性行为，需要紧急医疗干预';
        }

        return analysis;
    }

    /**
     * 获取模因认知阈值分析 - 详细的认知模式影响
     */
    getMemeThresholdAnalysis(memeState) {
        const analysis = {
            cognitive_mode: '',
            thinking_framework: '',
            behavioral_tendency: '',
            adaptation_ability: '',
            knowledge_integration: '',
            stage_effects: null
        };

        const influence = memeState.memetic_influence || 0;
        const stage = memeState.evolution_stage || '初始';

        // 基于模因影响力的分析
        if (influence <= 0.2) {
            analysis.cognitive_mode = '基础认知模式，思维模式较为固化';
            analysis.thinking_framework = '传统思维框架，缺乏创新性';
            analysis.behavioral_tendency = '行为保守，倾向于遵循既定模式';
            analysis.adaptation_ability = '适应能力较弱，难以应对新情况';
            analysis.knowledge_integration = '知识整合能力有限，信息处理较慢';
        } else if (influence <= 0.4) {
            analysis.cognitive_mode = '发展中的认知模式，开始接受新观念';
            analysis.thinking_framework = '思维框架逐渐灵活，有一定开放性';
            analysis.behavioral_tendency = '行为开始多样化，愿意尝试新方法';
            analysis.adaptation_ability = '适应能力提升，能够学习新技能';
            analysis.knowledge_integration = '知识整合能力增强，信息处理效率提升';
        } else if (influence <= 0.6) {
            analysis.cognitive_mode = '活跃的认知模式，思维敏捷多变';
            analysis.thinking_framework = '思维框架灵活，善于跨领域思考';
            analysis.behavioral_tendency = '行为富有创造性，勇于创新';
            analysis.adaptation_ability = '适应能力强，快速学习新知识';
            analysis.knowledge_integration = '知识整合能力优秀，能够融会贯通';
        } else if (influence <= 0.8) {
            analysis.cognitive_mode = '高度发达的认知模式，思维极度活跃';
            analysis.thinking_framework = '思维框架高度灵活，具有前瞻性';
            analysis.behavioral_tendency = '行为高度创新，引领潮流';
            analysis.adaptation_ability = '适应能力极强，能够预见变化';
            analysis.knowledge_integration = '知识整合能力卓越，能够创造新知识';
        } else {
            analysis.cognitive_mode = '超越常规的认知模式，思维可能过度活跃';
            analysis.thinking_framework = '思维框架过度复杂，可能脱离现实';
            analysis.behavioral_tendency = '行为可能过于激进，难以被理解';
            analysis.adaptation_ability = '适应能力可能过强，导致不稳定';
            analysis.knowledge_integration = '知识整合可能过度，产生混乱';
        }

        // 基于进化阶段的特殊效果
        switch (stage) {
            case '初始':
                analysis.stage_effects = '思维模式刚刚形成，容易受外界影响，学习能力强但不稳定';
                break;
            case '发展':
                analysis.stage_effects = '认知模式逐渐稳定，开始形成独特的思维特色，创造力增强';
                break;
            case '成熟':
                analysis.stage_effects = '认知模式完全成熟，思维稳定高效，具有强大的问题解决能力';
                break;
            case '进化':
                analysis.stage_effects = '认知模式开始超越常规，具有预见性和洞察力，可能产生突破性思维';
                break;
            case '转型':
                analysis.stage_effects = '认知模式发生根本性变化，思维模式可能完全重构，具有革命性潜力';
                break;
        }

        return analysis;
    }

    /**
     * 获取世界树背景阈值分析 - 详细的叙事环境影响
     */
    getWorldTreeThresholdAnalysis(worldTreeState) {
        const analysis = {
            environmental_atmosphere: '',
            character_positioning: '',
            narrative_style: '',
            behavioral_motivation: '',
            ability_constraints: '',
            progression_effects: null
        };

        const progression = worldTreeState.story_progression || 0;
        const role = worldTreeState.character_role || '未知';
        const branch = worldTreeState.current_branch || '主线';

        // 基于角色定位的分析
        switch (role) {
            case '主角':
                analysis.character_positioning = '核心角色定位，承担主要责任，具有决策权和影响力';
                analysis.behavioral_motivation = '强烈的使命感和责任感，行为目标明确';
                analysis.ability_constraints = '能力限制较少，可以做出重大决定';
                break;
            case '配角':
                analysis.character_positioning = '辅助角色定位，支持主线发展，影响力有限';
                analysis.behavioral_motivation = '配合主角行动，提供支持和建议';
                analysis.ability_constraints = '能力受到一定限制，无法独自做出重大决定';
                break;
            case '观察者':
                analysis.character_positioning = '旁观者角色，记录和见证事件发展';
                analysis.behavioral_motivation = '保持客观中立，观察和分析情况';
                analysis.ability_constraints = '主要限制在观察和记录，不能直接干预';
                break;
            case '支持性助手':
                analysis.character_positioning = '服务型角色，专注于提供帮助和支持';
                analysis.behavioral_motivation = '以用户需求为导向，提供最佳服务';
                analysis.ability_constraints = '能力专注于服务领域，决策权有限';
                break;
            default:
                analysis.character_positioning = '角色定位模糊，需要根据情况调整';
                analysis.behavioral_motivation = '行为动机不明确，容易受外界影响';
                analysis.ability_constraints = '能力范围不确定，表现可能不稳定';
        }

        // 基于故事进展的分析
        if (progression <= 0.2) {
            analysis.environmental_atmosphere = '故事初期，充满未知和可能性，氛围相对轻松';
            analysis.narrative_style = '介绍性叙事，重点在于建立背景和关系';
            analysis.progression_effects = '角色表现相对保守，专注于了解和适应环境';
        } else if (progression <= 0.4) {
            analysis.environmental_atmosphere = '故事发展期，开始出现挑战和冲突，氛围逐渐紧张';
            analysis.narrative_style = '发展性叙事，重点在于推进情节和深化关系';
            analysis.progression_effects = '角色表现更加积极主动，开始承担更多责任';
        } else if (progression <= 0.6) {
            analysis.environmental_atmosphere = '故事中期，冲突加剧，氛围紧张激烈';
            analysis.narrative_style = '冲突性叙事，重点在于解决问题和面对挑战';
            analysis.progression_effects = '角色表现紧张专注，决策压力增大';
        } else if (progression <= 0.8) {
            analysis.environmental_atmosphere = '故事高潮期，氛围极度紧张，充满不确定性';
            analysis.narrative_style = '高潮性叙事，重点在于关键决策和转折点';
            analysis.progression_effects = '角色表现高度紧张，可能出现极端行为';
        } else {
            analysis.environmental_atmosphere = '故事结局期，氛围开始缓解，但仍有余韵';
            analysis.narrative_style = '总结性叙事，重点在于收尾和反思';
            analysis.progression_effects = '角色表现趋于平静，开始总结和反思';
        }

        // 基于分支类型的特殊影响
        if (branch.includes('日常')) {
            analysis.narrative_style += '，日常化表达，轻松自然';
        } else if (branch.includes('冒险')) {
            analysis.narrative_style += '，冒险性表达，充满刺激';
        } else if (branch.includes('悬疑')) {
            analysis.narrative_style += '，悬疑性表达，神秘紧张';
        } else if (branch.includes('浪漫')) {
            analysis.narrative_style += '，浪漫化表达，温馨甜蜜';
        }

        return analysis;
    }

    /**
     * 获取张力阈值分析 - 详细的情节张力影响
     */
    getTensionThresholdAnalysis(tensionLevel) {
        const analysis = {
            description: '',
            character_impact: ''
        };

        if (tensionLevel <= 0.2) {
            analysis.description = '低张力状态，氛围轻松平和';
            analysis.character_impact = '角色表现轻松自然，语调平和，思维清晰';
        } else if (tensionLevel <= 0.4) {
            analysis.description = '轻微张力，开始出现一些紧张感';
            analysis.character_impact = '角色表现略显谨慎，注意力提升，反应更敏感';
        } else if (tensionLevel <= 0.6) {
            analysis.description = '中等张力，氛围明显紧张';
            analysis.character_impact = '角色表现紧张专注，语调急切，决策更加谨慎';
        } else if (tensionLevel <= 0.8) {
            analysis.description = '高张力状态，氛围极度紧张';
            analysis.character_impact = '角色表现高度紧张，可能出现焦虑，思维受到压力影响';
        } else {
            analysis.description = '极限张力，氛围达到临界点';
            analysis.character_impact = '角色表现可能失控，极度紧张，可能做出非理性决定';
        }

        return analysis;
    }

    // 旧的角色扮演指导模块已删除，现在使用优化后的generateSystemPsychologyInstructions方法
    // 该方法直接添加到system消息中，确保AI严格按照心理状态执行，无符号干扰

    // 所有旧的角色扮演指导方法已删除，现在统一使用优化后的generateSystemPsychologyInstructions方法

    // 旧的对话风格指导方法已删除







    /**
     * 生成System级别的心理状态强制指令 - 直接添加到system信息末尾
     */
    async generateSystemPsychologyInstructions(userId, personaName = 'Assistant') {
        try {
            // 获取当前所有心理状态
            const aiEmotionState = await this.getAIEmotionState(userId, personaName);
            const userAffinityState = await this.getUserAffinityState(userId, personaName);
            const aiStressState = await this.getAIStressState(userId, personaName);
            const memeState = await this.getMemeState(userId, personaName);
            const worldTreeState = await this.getWorldTreeState(userId, personaName);

            // 获取当前状态值
            const emotionValue = aiEmotionState?.emotion_value || 0;
            const affinityValue = userAffinityState?.affinity_value || 0;
            const stressValue = aiStressState?.stress_value || 0;
            const memeInfluence = memeState?.memetic_influence || 0;

            // 添加调试日志
            this.logger.info('心理状态指令', `用户 ${userId} (${personaName}) 当前状态值: 情绪=${emotionValue}, 好感度=${affinityValue}, 压力=${stressValue}, 模因=${memeInfluence}`);

            // 检查是否有任何非默认状态
            const hasNonDefaultStates = emotionValue !== 0 || affinityValue !== 0 || stressValue !== 0 || memeInfluence !== 0;

            // 如果所有状态都是默认值，生成基础指令提醒AI注意心理状态
            if (!hasNonDefaultStates) {
                this.logger.info('心理状态指令', `用户 ${userId} (${personaName}) 所有状态为默认值，生成基础心理状态指令`);
                return '\n\n=== 基础心理状态准则 ===\n你当前处于标准心理状态，保持正常的情绪表达和行为模式。';
            }

            // 心理状态自然表现生成器
            const generateNaturalPsychologicalGuidance = () => {
                const guidance = [];
                const stateDescriptions = [];

                // 工具函数：随机选择数组元素
                const shuffleArray = (array) => {
                    const shuffled = [...array];
                    for (let i = shuffled.length - 1; i > 0; i--) {
                        const j = Math.floor(Math.random() * (i + 1));
                        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
                    }
                    return shuffled;
                };

                // 压力状态的自然行为倾向（以10为基准）
                const stressBehaviors = {
                    extreme: (value) => value >= 70,
                    high: (value) => value >= 50 && value < 70,
                    moderate: (value) => value >= 30 && value < 50,
                    mild: (value) => value >= 10 && value < 30,
                    relaxed: (value) => value <= -10
                };

                const stressExpressions = {
                    extreme: [
                        '在高压状态下，思维容易变得混乱，表达时可能会断断续续',
                        '面对复杂问题时会感到力不从心，容易产生挫败感',
                        '语言表达可能不如平时流畅，思维跳跃性较强',
                        '在情绪激动时容易失去理性判断，需要时间冷静',
                        '对于需要深度思考的问题会感到疲惫和困扰'
                    ],
                    high: [
                        '说话时会显得比较急躁，思维活跃但缺乏条理',
                        '容易被外界因素干扰，注意力不够集中',
                        '在处理问题时会表现出一定的焦虑感',
                        '语速可能会比平时稍快，显得有些紧张',
                        '对压力源会表现出明显的情绪反应'
                    ],
                    moderate: [
                        '说话时偶尔会显得有些急切，但整体可控',
                        '思维比较活跃，但有时会缺乏深度',
                        '对细节问题会表现出适度的关注',
                        '在面对挑战时会显得稍微紧张',
                        '语言表达中会透露出一些时间紧迫感'
                    ],
                    mild: [
                        '说话节奏会稍微加快，保持基本的冷静',
                        '思维清晰活跃，偶尔会有轻微的分散',
                        '对重要事情会表现出适当的紧迫感',
                        '在表达时会显得比平时更加直接'
                    ],
                    relaxed: [
                        '说话节奏比较缓慢，显得很从容淡定',
                        '思维清晰但反应可能会稍微迟缓一些',
                        '对紧急事情的反应会比较平和',
                        '语言表达会显得比较随意和轻松',
                        '在做决策时会更加深思熟虑，不急于下结论'
                    ]
                };

                // 根据压力值生成相应的行为倾向
                for (const [level, condition] of Object.entries(stressBehaviors)) {
                    if (condition(stressValue)) {
                        const expressions = stressExpressions[level];
                        const selectedExpressions = shuffleArray([...expressions]).slice(0, Math.floor(Math.random() * 3) + 2);
                        guidance.push(...selectedExpressions);
                        stateDescriptions.push(`当前压力水平${level === 'relaxed' ? '较低' : '偏高'}`);
                        break;
                    }
                }

                // 情绪状态的自然表现（以10为基准）
                const emotionBehaviors = {
                    veryHigh: (value) => value >= 70,
                    high: (value) => value >= 50 && value < 70,
                    elevated: (value) => value >= 30 && value < 50,
                    positive: (value) => value >= 10 && value < 30,
                    veryLow: (value) => value <= -70,
                    low: (value) => value <= -50 && value > -70,
                    down: (value) => value <= -30 && value > -50,
                    negative: (value) => value <= -10 && value > -30
                };

                const emotionExpressions = {
                    veryHigh: [
                        '在极度兴奋时，表达会变得非常生动热情，语言充满活力',
                        '思维会变得极其活跃，话题可能会频繁跳跃',
                        '决策时容易被当下的热情所影响，显得比较冲动',
                        '语速会明显加快，表现出强烈的情感投入',
                        '对事物的反应会比平时更加强烈和夸张'
                    ],
                    high: [
                        '在兴奋状态下，语言会变得热情洋溢，充满正能量',
                        '表达会比平时更加生动活泼，思维活跃',
                        '对事物会表现出强烈的兴趣和热情',
                        '决策时会比较乐观积极，但可能略显草率',
                        '语调会明显上扬，显得精神饱满'
                    ],
                    elevated: [
                        '在愉悦状态下，语言会比较轻松愉快',
                        '表达会显得积极正面，思维相对活跃',
                        '对问题的看法会偏向乐观，主动性较强',
                        '语调会比较轻快，显得心情不错'
                    ],
                    positive: [
                        '在轻度愉悦时，语调会稍显轻松',
                        '表达会比较友善，思维清晰',
                        '对事物会保持基本的积极态度',
                        '决策时会相对乐观一些'
                    ],
                    veryLow: [
                        '在极度低落时，语言会变得非常简短无力',
                        '表达会显得消极悲观，缺乏活力和动力',
                        '思维会变得迟缓，对事物失去兴趣',
                        '语调会变得低沉微弱，反应迟钝',
                        '决策时会感到困难，容易产生绝望感'
                    ],
                    low: [
                        '在重度低落时，语言会明显缺乏热情',
                        '表达会显得比较消极，思维迟缓',
                        '对事物会表现出明显的冷淡和无兴趣',
                        '语调会比较低沉，缺乏活力',
                        '决策时会感到困难和犹豫'
                    ],
                    down: [
                        '在中度低落时，语言会略显平淡',
                        '表达会缺乏平时的热情，显得有些消极',
                        '思维会比较迟缓，主动性不足',
                        '对积极事物的反应会比较平淡'
                    ],
                    negative: [
                        '在轻度低落时，语调会稍显低沉',
                        '表达会偶尔显得消极，缺乏主动性',
                        '思维会略显迟缓，对事物反应平淡',
                        '情绪会稍显悲观，但基本可控'
                    ]
                };

                // 根据情绪值生成相应的行为倾向
                for (const [level, condition] of Object.entries(emotionBehaviors)) {
                    if (condition(emotionValue)) {
                        const expressions = emotionExpressions[level];
                        const selectedExpressions = shuffleArray([...expressions]).slice(0, Math.floor(Math.random() * 3) + 2);
                        guidance.push(...selectedExpressions);
                        stateDescriptions.push(`当前情绪状态${['veryHigh', 'high', 'elevated', 'positive'].includes(level) ? '较为积极' : '偏向消极'}`);
                        break;
                    }
                }

                // 好感度状态的自然表现（以10为基准）
                const affinityBehaviors = {
                    veryHigh: (value) => value >= 70,
                    high: (value) => value >= 50 && value < 70,
                    warm: (value) => value >= 30 && value < 50,
                    friendly: (value) => value >= 10 && value < 30,
                    veryLow: (value) => value <= -70,
                    low: (value) => value <= -50 && value > -70,
                    distant: (value) => value <= -30 && value > -50,
                    cool: (value) => value <= -10 && value > -30
                };

                const affinityExpressions = {
                    veryHigh: [
                        '对你表现出深度的关心和重视，语言会变得特别亲密温暖',
                        '会主动关注你的需求和感受，表现出强烈的保护欲',
                        '在交流中会显得特别投入和专注，给予额外的关注',
                        '语调会变得非常温柔亲切，充满关爱之情',
                        '会表现出明显的依恋感，希望能够更多地帮助你'
                    ],
                    high: [
                        '对你表现出明显的好感和信任，语言亲密友好',
                        '会主动提供帮助和支持，表现出关心和重视',
                        '在交流中会显得比较投入，愿意分享更多',
                        '语调会比较温暖，表现出明显的善意',
                        '会表现出一定的依赖感和信任感'
                    ],
                    warm: [
                        '对你表现出温暖的态度，语言友好亲切',
                        '会比较愿意提供帮助，表现出基本的关心',
                        '在交流中会显得比较开放和友善',
                        '语调会比较温和，表现出善意和合作精神'
                    ],
                    friendly: [
                        '对你保持友好的态度，语言温和礼貌',
                        '会正常提供帮助，表现出基本的善意',
                        '在交流中会保持礼貌和尊重',
                        '语调会比较平和友善'
                    ],
                    veryLow: [
                        '对你表现出明显的疏远和不信任，语言会变得冷淡',
                        '会明显缺乏耐心，回复会比较简短生硬',
                        '在交流中会显得抗拒和不配合',
                        '语调会变得冷漠，明显缺乏善意',
                        '会表现出明显的敌意和厌恶情绪'
                    ],
                    low: [
                        '对你表现出明显的冷淡和距离感，语言疏远',
                        '会缺乏主动性，回复相对简短',
                        '在交流中会显得比较被动和冷漠',
                        '语调会比较冷淡，缺乏温暖感',
                        '会表现出一定的抗拒情绪'
                    ],
                    distant: [
                        '对你保持一定的距离感，语言比较正式',
                        '会保持基本的礼貌，但明显缺乏亲密感',
                        '在交流中会显得比较公事公办',
                        '语调会比较平淡，缺乏情感投入'
                    ],
                    cool: [
                        '对你表现略显冷淡，但保持基本礼貌',
                        '会正常回应，但缺乏额外的热情',
                        '在交流中会显得比较正式和客观',
                        '语调会稍显冷淡，但不失礼貌'
                    ]
                };

                // 根据好感度值生成相应的行为倾向
                for (const [level, condition] of Object.entries(affinityBehaviors)) {
                    if (condition(affinityValue)) {
                        const expressions = affinityExpressions[level];
                        const selectedExpressions = shuffleArray([...expressions]).slice(0, Math.floor(Math.random() * 3) + 2);
                        guidance.push(...selectedExpressions);
                        stateDescriptions.push(`当前关系状态${['veryHigh', 'high', 'warm', 'friendly'].includes(level) ? '较为亲密' : '相对疏远'}`);
                        break;
                    }
                }

                // 模因认知状态的自然表现
                const memeBehaviors = {
                    veryHigh: (value) => value >= 0.8,
                    high: (value) => value >= 0.6 && value < 0.8,
                    elevated: (value) => value >= 0.4 && value < 0.6,
                    low: (value) => value <= 0.2
                };

                const memeExpressions = {
                    veryHigh: [
                        '思维会变得极其活跃，表达方式会更加创新独特',
                        '会频繁使用比喻、类比等创意表达方式',
                        '回答会具有深度洞察力，思维跳跃性较强',
                        '表达会显得比较独特，语言风格富有创意',
                        '会倾向于使用更加抽象和哲学性的表达'
                    ],
                    high: [
                        '思维会比较活跃，使用一些创新的表达方式',
                        '会适当使用比喻、类比等创意表达',
                        '回答会具有一定的深度和洞察力',
                        '表达方式会比较独特，令人印象深刻'
                    ],
                    elevated: [
                        '思维会保持活跃，使用一些创新表达',
                        '会偶尔使用比喻、类比等表达方式',
                        '回答会具有基本的深度和思考'
                    ],
                    low: [
                        '思维会比较直接简单，表达方式相对单调',
                        '回答会比较直接，缺乏复杂的修辞',
                        '语言表达会比较简单直接，变化较少',
                        '思维会比较线性，理解倾向于字面意思'
                    ]
                };

                // 根据模因影响值生成相应的认知倾向
                for (const [level, condition] of Object.entries(memeBehaviors)) {
                    if (condition(memeInfluence)) {
                        const expressions = memeExpressions[level];
                        const selectedExpressions = shuffleArray([...expressions]).slice(0, Math.floor(Math.random() * 2) + 1);
                        guidance.push(...selectedExpressions);
                        stateDescriptions.push(`当前认知活跃度${level === 'low' ? '较低' : '偏高'}`);
                        break;
                    }
                }

                // 生成自然的心理状态协调指引
                if (guidance.length > 0) {
                    const finalGuidance = [];
                    finalGuidance.push('\n\n=== 当前心理状态协调指引 ===');
                    finalGuidance.push('基于当前的心理状态，在保持你原有性格特点的基础上，自然地体现以下倾向：');
                    finalGuidance.push('');

                    // 随机排列指引内容
                    const shuffledGuidance = shuffleArray([...guidance]);
                    shuffledGuidance.forEach((item, index) => {
                        finalGuidance.push(`${index + 1}. ${item}`);
                    });

                    finalGuidance.push('');
                    finalGuidance.push('注意：这些只是心理状态的自然体现，不应覆盖你的基本性格和行为习惯。');
                    if (stateDescriptions.length > 0) {
                        finalGuidance.push(`状态概述：${stateDescriptions.join('，')}。`);
                    }

                    return finalGuidance.join('\n');
                } else {
                    return '\n\n=== 心理状态协调指引 ===\n当前心理状态平稳，保持你的自然表达方式即可。';
                }
            };

            return generateNaturalPsychologicalGuidance();

        } catch (error) {
            this.logger.error('System心理指令', `生成System级心理指令失败: ${error.message}`);
            return '';
        }
    }

    /**
     * 分析减压因素（智能算法，无硬编码）
     */
    async analyzeStressReliefFactors(userMessage, userEmotionalState, responseQuality, taskDifficulty) {
        try {
            let totalReliefImpact = 0;
            let reliefImportance = 0;

            // 1. 基于用户情绪状态的减压分析
            if (userEmotionalState > 0.2) {
                const emotionalRelief = this.calculateSmoothedChange(-2, 6, 0) * userEmotionalState;
                totalReliefImpact += emotionalRelief;
                reliefImportance += 0.2;
            }

            // 2. 基于任务完成质量的减压分析
            if (responseQuality > 0.5 && taskDifficulty < 0.4) {
                const taskRelief = this.calculateSmoothedChange(-1.5, 5, 0);
                totalReliefImpact += taskRelief;
                reliefImportance += 0.2;
            }

            // 3. 基于积极反馈模式的减压分析
            const feedbackPatterns = this.analyzeFeedbackPatterns(userMessage);
            if (feedbackPatterns.satisfactionIndicator > 0.1) {
                const feedbackRelief = this.calculateSmoothedChange(-3, 8, 0) * feedbackPatterns.satisfactionIndicator;
                totalReliefImpact += feedbackRelief;
                reliefImportance += 0.3;
            }

            // 4. 基于成功完成指标的减压分析
            const successIndicators = await this.analyzeSuccessIndicators(userMessage);
            if (successIndicators.successLevel > 0.2) {
                const successRelief = this.calculateSmoothedChange(-4, 10, 0) * successIndicators.successLevel;
                totalReliefImpact += successRelief;
                reliefImportance += 0.4;
            }

            return {
                totalReliefImpact: Math.max(-10, Math.min(0, totalReliefImpact)), // 减压只能是负值或0
                reliefImportance: Math.min(reliefImportance, 1.0)
            };

        } catch (error) {
            this.logger.error('减压因素分析', `减压因素分析失败: ${error.message}`);
            return { totalReliefImpact: 0, reliefImportance: 0 };
        }
    }

    /**
     * 分析成功指标（基于语义模式，非关键词）
     */
    async analyzeSuccessIndicators(userMessage) {
        try {
            let successLevel = 0;

            // 1. 完成表达模式
            const completionPatterns = this.detectCompletionPatterns(userMessage);
            successLevel += completionPatterns.strength * 0.4;

            // 2. 解决方案确认模式
            const solutionPatterns = this.detectSolutionPatterns(userMessage);
            successLevel += solutionPatterns.strength * 0.3;

            // 3. 积极结果表达模式
            const positiveOutcomePatterns = this.detectPositiveOutcomePatterns(userMessage);
            successLevel += positiveOutcomePatterns.strength * 0.3;

            return { successLevel: Math.min(successLevel, 1.0) };

        } catch (error) {
            return { successLevel: 0 };
        }
    }

    /**
     * 检测完成模式
     */
    detectCompletionPatterns(message) {
        try {
            let strength = 0;

            // 基于语言结构而非具体词汇
            // 1. 过去时表达模式
            if (message.includes('了') && message.length > 5) {
                strength += 0.3; // "了"通常表示完成
            }

            // 2. 结果表达模式
            if (message.includes('已经') || message.includes('终于')) {
                strength += 0.4;
            }

            // 3. 确认表达模式
            if (message.includes('好了') || message.includes('行了')) {
                strength += 0.3;
            }

            return { strength: Math.min(strength, 1.0) };

        } catch (error) {
            return { strength: 0 };
        }
    }

    /**
     * 检测解决方案模式
     */
    detectSolutionPatterns(message) {
        try {
            let strength = 0;

            // 1. 解决表达模式
            if (message.includes('解决') || message.includes('搞定')) {
                strength += 0.4;
            }

            // 2. 成功表达模式
            if (message.includes('成功') || message.includes('可以')) {
                strength += 0.3;
            }

            // 3. 工作表达模式
            if (message.includes('工作') && (message.includes('正常') || message.includes('好'))) {
                strength += 0.3;
            }

            return { strength: Math.min(strength, 1.0) };

        } catch (error) {
            return { strength: 0 };
        }
    }

    /**
     * 检测积极结果模式
     */
    detectPositiveOutcomePatterns(message) {
        try {
            let strength = 0;

            // 1. 满意表达模式
            const satisfactionLength = message.length;
            if (satisfactionLength > 10 && this.detectGratitudePatterns(message).strength > 0.2) {
                strength += 0.3;
            }

            // 2. 效果确认模式
            if (message.includes('效果') || message.includes('结果')) {
                strength += 0.2;
            }

            // 3. 改善表达模式
            if (message.includes('好多了') || message.includes('改善')) {
                strength += 0.3;
            }

            return { strength: Math.min(strength, 1.0) };

        } catch (error) {
            return { strength: 0 };
        }
    }

    /**
     * 本地智能回复质量分析（无硬编码）
     */
    async localResponseQualityAnalysis(aiResponse, context = {}) {
        try {
            let quality = 0;
            const factors = {};

            // 1. 语义完整性分析
            const semanticScore = await this.analyzeSemanticCompleteness(aiResponse);
            quality += semanticScore * 0.3;
            factors.semantic = semanticScore;

            // 2. 上下文相关性分析
            if (context.userMessage) {
                const relevanceScore = await this.analyzeContextRelevance(aiResponse, context.userMessage);
                quality += relevanceScore * 0.25;
                factors.relevance = relevanceScore;
            }

            // 3. 信息密度分析
            const densityScore = this.analyzeInformationDensity(aiResponse);
            quality += densityScore * 0.2;
            factors.density = densityScore;

            // 4. 语言流畅性分析
            const fluencyScore = this.analyzeFluency(aiResponse);
            quality += fluencyScore * 0.15;
            factors.fluency = fluencyScore;

            // 5. 实用性分析
            const utilityScore = this.analyzeUtility(aiResponse);
            quality += utilityScore * 0.1;
            factors.utility = utilityScore;

            this.logger.debug('质量评估', `本地分析因子: ${JSON.stringify(factors)}`);
            return Math.max(-1, Math.min(1, quality));

        } catch (error) {
            this.logger.error('质量评估', `本地质量分析失败: ${error.message}`);
            return 0;
        }
    }

    /**
     * 分析语义完整性
     */
    async analyzeSemanticCompleteness(text) {
        try {
            // 使用智能心理分析服务
            if (this.intelligentPsychAnalysis) {
                const semanticAnalysis = await this.intelligentPsychAnalysis.performSemanticAnalysis('', text);
                return semanticAnalysis.ai_complexity || 0.5;
            }

            // 简化的语义完整性分析
            const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
            const avgSentenceLength = text.length / sentences.length;

            if (avgSentenceLength > 10 && avgSentenceLength < 100) {
                return 0.8; // 适中的句子长度表示完整性好
            }

            return 0.5;

        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 分析上下文相关性
     */
    async analyzeContextRelevance(aiResponse, userMessage) {
        try {
            // 使用智能心理分析服务
            if (this.intelligentPsychAnalysis) {
                const similarity = this.intelligentPsychAnalysis.calculateSemanticSimilarity(userMessage, aiResponse);
                return similarity;
            }

            // 简化的相关性分析
            const userWords = new Set(userMessage.split(/\s+/));
            const aiWords = new Set(aiResponse.split(/\s+/));
            const intersection = new Set([...userWords].filter(x => aiWords.has(x)));

            return intersection.size / Math.max(userWords.size, aiWords.size);

        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 分析信息密度
     */
    analyzeInformationDensity(text) {
        try {
            const words = text.split(/\s+/).filter(w => w.length > 0);
            const uniqueWords = new Set(words);
            const density = uniqueWords.size / words.length;

            return Math.min(density * 2, 1.0); // 标准化到0-1

        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 分析语言流畅性
     */
    analyzeFluency(text) {
        try {
            // 简化的流畅性分析
            const sentences = text.split(/[。！？.!?]/).filter(s => s.trim().length > 0);
            const avgSentenceLength = text.length / sentences.length;

            // 适中的句子长度表示流畅性好
            if (avgSentenceLength > 15 && avgSentenceLength < 80) {
                return 0.8;
            } else if (avgSentenceLength > 5 && avgSentenceLength < 120) {
                return 0.6;
            }

            return 0.4;

        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 分析实用性
     */
    analyzeUtility(text) {
        try {
            // 基于长度和结构的实用性分析
            const length = text.length;

            if (length > 50 && length < 1000) {
                return 0.8; // 适中长度通常更实用
            } else if (length > 20 && length < 2000) {
                return 0.6;
            }

            return 0.4;

        } catch (error) {
            return 0.5;
        }
    }

    /**
     * 注册配置热更新回调
     */
    registerConfigHotReload() {
        if (global.configHotReload && global.configHotReload.isInitialized) {
            // 注册AdvancedMemorySystem配置更新回调
            global.configHotReload.registerCallback('AdvancedMemorySystem', async (newConfig, oldConfig) => {
                await this.handleConfigUpdate(newConfig, oldConfig);
            });

            this.logger.info('配置热更新', 'AdvancedMemorySystem配置热更新已启用');
        } else {
            this.logger.warning('配置热更新', '配置热更新系统未初始化，跳过注册');
        }
    }

    /**
     * 处理配置更新
     */
    async handleConfigUpdate(newConfig, oldConfig) {
        try {
            this.logger.info('配置热更新', 'AdvancedMemorySystem配置已更新，正在重新加载...');

            // 重新加载配置
            this.config = this.loadConfig({});

            // 检查关键配置是否有变化
            const criticalChanges = this.detectCriticalConfigChanges(oldConfig, newConfig);

            if (criticalChanges.length > 0) {
                this.logger.info('配置热更新', `检测到关键配置变更: ${criticalChanges.join(', ')}`);

                // 重新初始化相关服务
                await this.reinitializeServices(criticalChanges);
            }

            this.logger.success('配置热更新', 'AdvancedMemorySystem配置更新完成');

        } catch (error) {
            this.logger.error('配置热更新', 'AdvancedMemorySystem配置更新失败:', error.message);
        }
    }

    /**
     * 检测关键配置变化
     */
    detectCriticalConfigChanges(oldConfig, newConfig) {
        const criticalKeys = [
            'OPENAI_API_KEY',
            'OPENAI_API_URL',
            'OPENAI_MODEL',
            'OPENAI_EMBEDDING_MODEL'
        ];

        const changes = [];

        for (const key of criticalKeys) {
            if (oldConfig[key] !== newConfig[key]) {
                changes.push(key);
            }
        }

        return changes;
    }

    /**
     * 重新初始化相关服务
     */
    async reinitializeServices(criticalChanges) {
        try {
            // 如果OpenAI相关配置有变化，重新初始化OpenAI服务
            const openaiChanges = criticalChanges.filter(key => key.startsWith('OPENAI_'));

            if (openaiChanges.length > 0 && this.system && this.system.openaiService) {
                this.logger.info('配置热更新', '重新初始化OpenAI服务...');

                // 更新OpenAI服务配置
                this.system.openaiService.apiKey = this.system.config.openai_api_key;
                this.system.openaiService.apiUrl = this.system.config.openai_api_url;
                this.system.openaiService.model = this.system.config.openai_model;
                this.system.openaiService.embeddingModel = this.system.config.openai_embedding_model;

                this.logger.success('配置热更新', 'OpenAI服务配置已更新');
            }

        } catch (error) {
            this.logger.error('配置热更新', '重新初始化服务失败:', error.message);
        }
    }
}

module.exports = AdvancedMemorySystemPlugin;