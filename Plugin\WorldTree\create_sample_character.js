// Plugin/WorldTree/create_sample_character.js - 创建示例角色
const WorldTreePlugin = require('./PluginInterface.js');

async function createSampleCharacter() {
    console.log('🌳 创建示例角色 - 雨安安...\n');
    
    try {
        // 创建插件实例
        const plugin = new WorldTreePlugin();
        
        // 配置
        const config = {
            DebugMode: false,
            AI_API_URL: 'https://api.openai.com/v1',
            AI_API_KEY: process.env.OPENAI_API_KEY || 'test_key',
            AI_MODEL: 'gpt-4o-mini',
            PSYCHOLOGY_GENERATION_ENABLED: false, // 暂时禁用AI调用
            MAX_PSYCHOLOGY_LENGTH: 500,
            SCHEDULE_UPDATE_INTERVAL: 60
        };
        
        // 初始化插件
        console.log('初始化插件...');
        await plugin.initialize(config);
        
        // 创建雨安安角色
        console.log('创建角色：雨安安');
        const createResult = await plugin.execute({
            action: 'create_character',
            character_name: '雨安安',
            time_structure: '现代都市设定，2024年12月，大学生，就读于某知名理工大学计算机科学专业',
            description: '活泼开朗的大学生，喜欢动漫、编程和游戏。性格温和友善，对新技术充满好奇心。平时喜欢熬夜写代码，经常因为专注编程而忘记吃饭。'
        });
        
        if (!createResult.success) {
            console.log('角色可能已存在，继续添加日程...');
        } else {
            console.log('✅ 角色创建成功');
        }
        
        // 添加日程安排
        console.log('\n添加日程安排...');
        
        // 1. 日常课程
        await plugin.execute({
            action: 'add_schedule',
            character_name: '雨安安',
            schedule_type: 'daily',
            title: '数据结构与算法课',
            description: '周一到周五上午的核心专业课程',
            start_time: '2024-12-19T09:00:00Z',
            end_time: '2024-12-19T11:00:00Z',
            recurrence_rule: '0 9 * * 1-5',
            priority: 3
        });
        
        // 2. 编程项目
        await plugin.execute({
            action: 'add_schedule',
            character_name: '雨安安',
            schedule_type: 'event',
            title: '完成期末项目',
            description: '开发一个基于AI的聊天机器人系统',
            start_time: '2024-12-19T14:00:00Z',
            end_time: '2024-12-25T23:59:59Z',
            priority: 4
        });
        
        // 3. 休闲活动
        await plugin.execute({
            action: 'add_schedule',
            character_name: '雨安安',
            schedule_type: 'weekly',
            title: '看新番动漫',
            description: '每周末放松时间，追最新的动漫番剧',
            start_time: '2024-12-21T20:00:00Z',
            end_time: '2024-12-21T22:00:00Z',
            recurrence_rule: '0 20 * * 6',
            priority: 1
        });
        
        console.log('✅ 日程添加完成');
        
        // 更新角色状态
        console.log('\n更新角色状态...');
        await plugin.execute({
            action: 'update_character_state',
            character_name: '雨安安',
            current_mood: '专注',
            energy_level: 0.7,
            stress_level: 0.4,
            active_goals: ['完成期末项目', '准备算法考试', '学习新的编程框架'],
            personality_traits: {
                openness: 0.8,
                conscientiousness: 0.7,
                extraversion: 0.6,
                agreeableness: 0.9,
                neuroticism: 0.3,
                interests: ['编程', '动漫', '游戏', 'AI技术'],
                habits: ['熬夜编程', '喝咖啡', '听音乐写代码']
            }
        });
        
        console.log('✅ 角色状态更新完成');
        
        // 获取完整的系统消息
        console.log('\n生成系统消息...');
        const systemMessageResult = await plugin.execute({
            action: 'get_system_message',
            character_name: '雨安安'
        });
        
        console.log('\n📋 生成的系统消息：');
        console.log('=' * 60);
        console.log(systemMessageResult.system_message);
        console.log('=' * 60);
        
        // 获取角色列表
        console.log('\n📊 当前所有角色：');
        const charactersResult = await plugin.execute({
            action: 'get_characters'
        });
        
        charactersResult.characters.forEach((char, index) => {
            console.log(`${index + 1}. ${char.name} - ${char.time_structure}`);
            console.log(`   状态: ${char.current_mood || '未知'} | 精力: ${char.energy_level || 'N/A'} | 压力: ${char.stress_level || 'N/A'}`);
        });
        
        // 清理资源
        await plugin.cleanup();
        
        console.log('\n✅ 示例角色创建完成！');
        console.log('\n💡 使用提示：');
        console.log('1. 在API请求中设置 assistantName 为 "雨安安"');
        console.log('2. 系统会自动注入角色上下文到system消息');
        console.log('3. AI将基于角色设定进行回应');
        
    } catch (error) {
        console.error('\n❌ 创建示例角色失败:', error.message);
        process.exit(1);
    }
}

// 运行脚本
if (require.main === module) {
    createSampleCharacter();
}
