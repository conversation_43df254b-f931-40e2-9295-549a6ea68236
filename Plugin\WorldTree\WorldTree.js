// Plugin/WorldTree/WorldTree.js - 世界树角色管理系统
const fs = require('fs').promises;
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const { promisify } = require('util');
const logger = require('../../utils/logger.cjs');

class WorldTreeSystem {
    constructor(pluginDir, config) {
        this.pluginDir = pluginDir;
        this.config = config;
        this.db = null;
        this.dbRun = null;
        this.dbGet = null;
        this.dbAll = null;
        this.isInitialized = false;
        
        // 调试模式
        this.debugMode = config.DebugMode || false;
        
        // AI配置
        this.aiConfig = {
            apiUrl: config.AI_API_URL || 'https://api.openai.com/v1',
            apiKey: config.AI_API_KEY,
            model: config.AI_MODEL || 'gpt-4o-mini',
            psychologyEnabled: config.PSYCHOLOGY_GENERATION_ENABLED !== false,
            maxPsychologyLength: config.MAX_PSYCHOLOGY_LENGTH || 500
        };
        
        // 日程更新间隔
        this.scheduleUpdateInterval = config.SCHEDULE_UPDATE_INTERVAL || 60;
        
        this.log('info', '世界树系统初始化', {
            pluginDir: this.pluginDir,
            debugMode: this.debugMode,
            psychologyEnabled: this.aiConfig.psychologyEnabled
        });
    }

    /**
     * 日志记录方法
     */
    log(level, message, data = null) {
        const logMessage = `[世界树] ${message}`;
        if (data && this.debugMode) {
            logger[level]('世界树系统', logMessage, data);
        } else {
            logger[level]('世界树系统', logMessage);
        }
    }

    /**
     * 初始化数据库
     */
    async initializeDatabase() {
        try {
            this.log('info', '开始初始化数据库...');
            
            // 确保数据目录存在
            const dataDir = path.join(this.pluginDir, 'data');
            await fs.mkdir(dataDir, { recursive: true });
            
            // 数据库路径
            const dbPath = path.join(dataDir, 'world_tree.db');
            this.log('info', `数据库路径: ${dbPath}`);
            
            // 连接数据库
            this.db = new sqlite3.Database(dbPath);
            this.dbRun = promisify(this.db.run.bind(this.db));
            this.dbGet = promisify(this.db.get.bind(this.db));
            this.dbAll = promisify(this.db.all.bind(this.db));
            
            // 创建数据表
            await this.createTables();
            
            this.isInitialized = true;
            this.log('success', '数据库初始化完成');
            
        } catch (error) {
            this.log('error', `数据库初始化失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 创建数据库表
     */
    async createTables() {
        try {
            // 角色表
            await this.dbRun(`
                CREATE TABLE IF NOT EXISTS characters (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    time_structure TEXT NOT NULL,
                    description TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 日程表
            await this.dbRun(`
                CREATE TABLE IF NOT EXISTS schedules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    character_name TEXT NOT NULL,
                    schedule_type TEXT NOT NULL, -- daily, weekly, monthly, event
                    title TEXT NOT NULL,
                    description TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    recurrence_rule TEXT, -- cron-like format
                    priority INTEGER DEFAULT 1, -- 1-5
                    status TEXT DEFAULT 'active', -- active, completed, cancelled
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (character_name) REFERENCES characters(name)
                )
            `);

            // 心理活动记录表
            await this.dbRun(`
                CREATE TABLE IF NOT EXISTS psychology_activities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    character_name TEXT NOT NULL,
                    activity_type TEXT NOT NULL, -- thought, emotion, memory, reaction
                    content TEXT NOT NULL,
                    context_summary TEXT,
                    emotional_state TEXT,
                    stress_level REAL,
                    generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (character_name) REFERENCES characters(name)
                )
            `);

            // 角色状态表
            await this.dbRun(`
                CREATE TABLE IF NOT EXISTS character_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    character_name TEXT UNIQUE NOT NULL,
                    current_mood TEXT,
                    energy_level REAL DEFAULT 1.0,
                    stress_level REAL DEFAULT 0.0,
                    last_interaction DATETIME,
                    active_goals TEXT, -- JSON array
                    personality_traits TEXT, -- JSON object
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (character_name) REFERENCES characters(name)
                )
            `);

            // 创建索引
            await this.dbRun(`CREATE INDEX IF NOT EXISTS idx_schedules_character ON schedules(character_name)`);
            await this.dbRun(`CREATE INDEX IF NOT EXISTS idx_schedules_time ON schedules(start_time)`);
            await this.dbRun(`CREATE INDEX IF NOT EXISTS idx_psychology_character ON psychology_activities(character_name)`);
            await this.dbRun(`CREATE INDEX IF NOT EXISTS idx_psychology_time ON psychology_activities(generated_at)`);

            this.log('success', '数据库表创建完成');
            
        } catch (error) {
            this.log('error', `创建数据库表失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 创建或更新Agent配置
     */
    async createOrUpdateAgentConfig(agentName, timeStructure, description = '') {
        try {
            this.log('info', `创建或更新Agent配置: ${agentName}`);

            // 检查Agent配置是否已存在
            const existing = await this.dbGet(
                'SELECT name FROM characters WHERE name = ?',
                [agentName]
            );

            if (existing) {
                // 更新现有配置
                await this.dbRun(
                    'UPDATE characters SET time_structure = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE name = ?',
                    [timeStructure, description, agentName]
                );
                this.log('success', `Agent ${agentName} 配置更新成功`);
                return { success: true, message: `Agent ${agentName} 配置更新成功` };
            } else {
                // 插入新配置
                await this.dbRun(
                    'INSERT INTO characters (name, time_structure, description) VALUES (?, ?, ?)',
                    [agentName, timeStructure, description]
                );

                // 初始化Agent状态
                await this.dbRun(
                    `INSERT INTO character_states (character_name, current_mood, personality_traits)
                     VALUES (?, ?, ?)`,
                    [agentName, 'neutral', JSON.stringify({})]
                );

                this.log('success', `Agent ${agentName} 配置创建成功`);
                return { success: true, message: `Agent ${agentName} 配置创建成功` };
            }

        } catch (error) {
            this.log('error', `创建或更新Agent配置失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取角色列表
     */
    async getCharacters() {
        try {
            const characters = await this.dbAll(`
                SELECT c.*, cs.current_mood, cs.energy_level, cs.stress_level, cs.last_interaction
                FROM characters c
                LEFT JOIN character_states cs ON c.name = cs.character_name
                ORDER BY c.created_at DESC
            `);
            
            return { success: true, characters };
            
        } catch (error) {
            this.log('error', `获取角色列表失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取角色完整上下文
     */
    async getCharacterContext(characterName, includeSchedule = true, includePsychology = true) {
        try {
            this.log('info', `获取角色上下文: ${characterName}`);
            
            // 获取角色基本信息
            const character = await this.dbGet(
                'SELECT * FROM characters WHERE name = ?',
                [characterName]
            );
            
            if (!character) {
                return { success: false, error: `角色 ${characterName} 不存在` };
            }
            
            // 获取角色状态
            const state = await this.dbGet(
                'SELECT * FROM character_states WHERE character_name = ?',
                [characterName]
            );
            
            let context = {
                character,
                state,
                schedules: [],
                psychology: []
            };
            
            // 获取当前日程
            if (includeSchedule) {
                context.schedules = await this.getCurrentSchedules(characterName);
            }
            
            // 获取最近心理活动
            if (includePsychology) {
                context.psychology = await this.getRecentPsychology(characterName, 5);
            }
            
            return { success: true, context };
            
        } catch (error) {
            this.log('error', `获取角色上下文失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取当前日程
     */
    async getCurrentSchedules(characterName, limit = 10) {
        try {
            const now = new Date().toISOString();
            const schedules = await this.dbAll(`
                SELECT * FROM schedules
                WHERE character_name = ?
                AND status = 'active'
                AND (start_time >= ? OR end_time >= ?)
                ORDER BY start_time ASC
                LIMIT ?
            `, [characterName, now, now, limit]);

            return schedules;

        } catch (error) {
            this.log('error', `获取当前日程失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 获取最近心理活动
     */
    async getRecentPsychology(characterName, limit = 5) {
        try {
            const psychology = await this.dbAll(`
                SELECT * FROM psychology_activities
                WHERE character_name = ?
                ORDER BY generated_at DESC
                LIMIT ?
            `, [characterName, limit]);

            return psychology;

        } catch (error) {
            this.log('error', `获取最近心理活动失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 添加日程
     */
    async addSchedule(characterName, scheduleData) {
        try {
            const {
                scheduleType = 'event',
                title,
                description = '',
                startTime,
                endTime,
                recurrenceRule = '',
                priority = 1
            } = scheduleData;

            await this.dbRun(`
                INSERT INTO schedules
                (character_name, schedule_type, title, description, start_time, end_time, recurrence_rule, priority)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [characterName, scheduleType, title, description, startTime, endTime, recurrenceRule, priority]);

            this.log('success', `为角色 ${characterName} 添加日程: ${title}`);
            return { success: true, message: '日程添加成功' };

        } catch (error) {
            this.log('error', `添加日程失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成心理活动
     */
    async generatePsychology(characterName, conversationContext, memoryData) {
        try {
            if (!this.aiConfig.psychologyEnabled || !this.aiConfig.apiKey) {
                this.log('warning', '心理活动生成未启用或API密钥未配置');
                return { success: false, error: '心理活动生成未启用' };
            }

            this.log('info', `为角色 ${characterName} 生成心理活动`);

            // 获取角色上下文
            const contextResult = await this.getCharacterContext(characterName, true, true);
            if (!contextResult.success) {
                return contextResult;
            }

            const { context } = contextResult;

            // 构建AI请求
            const prompt = this.buildPsychologyPrompt(context, conversationContext, memoryData);

            // 调用AI API
            const psychologyContent = await this.callAIAPI(prompt);

            if (psychologyContent) {
                // 保存心理活动
                await this.savePsychologyActivity(characterName, psychologyContent, conversationContext);

                this.log('success', `角色 ${characterName} 心理活动生成成功`);
                return {
                    success: true,
                    psychology: psychologyContent,
                    message: '心理活动生成成功'
                };
            } else {
                return { success: false, error: 'AI API调用失败' };
            }

        } catch (error) {
            this.log('error', `生成心理活动失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 构建心理活动生成提示词
     */
    buildPsychologyPrompt(context, conversationContext, memoryData) {
        const { character, state, schedules, psychology } = context;

        let prompt = `你是一个专业的心理活动生成器。请为角色"${character.name}"生成当前的心理活动。

角色设定：
- 名称：${character.name}
- 时间架构：${character.time_structure}
- 描述：${character.description || '无'}

当前状态：
- 心情：${state?.current_mood || '未知'}
- 精力水平：${state?.energy_level || 1.0}
- 压力水平：${state?.stress_level || 0.0}

当前日程：
${schedules.length > 0 ? schedules.map(s => `- ${s.title}: ${s.description}`).join('\n') : '无当前日程'}

最近心理活动：
${psychology.length > 0 ? psychology.map(p => `- ${p.activity_type}: ${p.content}`).join('\n') : '无历史记录'}

对话上下文：
${conversationContext || '无当前对话'}

记忆数据：
${memoryData ? JSON.stringify(memoryData, null, 2) : '无记忆数据'}

请生成一段简洁的心理活动描述（不超过${this.aiConfig.maxPsychologyLength}字），包括：
1. 当前的思考状态
2. 情绪反应
3. 对当前情况的内心感受
4. 可能的行为倾向

要求：
- 符合角色设定和时间架构
- 体现角色的个性特点
- 与当前对话和记忆相关
- 语言自然流畅，第一人称视角
- 不要包含任何格式标记，直接输出心理活动内容`;

        return prompt;
    }

    /**
     * 调用AI API
     */
    async callAIAPI(prompt) {
        try {
            const fetch = (await import('node-fetch')).default;

            const response = await fetch(`${this.aiConfig.apiUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.aiConfig.apiKey}`
                },
                body: JSON.stringify({
                    model: this.aiConfig.model,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: Math.min(this.aiConfig.maxPsychologyLength * 2, 1000),
                    temperature: 0.7
                })
            });

            if (!response.ok) {
                throw new Error(`AI API请求失败: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            const content = data.choices?.[0]?.message?.content;

            if (!content) {
                throw new Error('AI API返回内容为空');
            }

            return content.trim();

        } catch (error) {
            this.log('error', `AI API调用失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 保存心理活动
     */
    async savePsychologyActivity(characterName, content, contextSummary) {
        try {
            await this.dbRun(`
                INSERT INTO psychology_activities
                (character_name, activity_type, content, context_summary)
                VALUES (?, ?, ?, ?)
            `, [characterName, 'thought', content, contextSummary || '']);

            this.log('info', `保存心理活动: ${characterName}`);

        } catch (error) {
            this.log('error', `保存心理活动失败: ${error.message}`);
        }
    }

    /**
     * 更新角色状态
     */
    async updateCharacterState(characterName, stateData) {
        try {
            const {
                currentMood,
                energyLevel,
                stressLevel,
                activeGoals,
                personalityTraits
            } = stateData;

            const updates = [];
            const values = [];

            if (currentMood !== undefined) {
                updates.push('current_mood = ?');
                values.push(currentMood);
            }
            if (energyLevel !== undefined) {
                updates.push('energy_level = ?');
                values.push(energyLevel);
            }
            if (stressLevel !== undefined) {
                updates.push('stress_level = ?');
                values.push(stressLevel);
            }
            if (activeGoals !== undefined) {
                updates.push('active_goals = ?');
                values.push(JSON.stringify(activeGoals));
            }
            if (personalityTraits !== undefined) {
                updates.push('personality_traits = ?');
                values.push(JSON.stringify(personalityTraits));
            }

            updates.push('last_interaction = CURRENT_TIMESTAMP');
            updates.push('updated_at = CURRENT_TIMESTAMP');
            values.push(characterName);

            await this.dbRun(`
                UPDATE character_states
                SET ${updates.join(', ')}
                WHERE character_name = ?
            `, values);

            this.log('success', `角色状态更新成功: ${characterName}`);
            return { success: true, message: '角色状态更新成功' };

        } catch (error) {
            this.log('error', `更新角色状态失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成系统消息内容
     */
    async generateSystemMessage(characterName) {
        try {
            this.log('info', `生成系统消息: ${characterName}`);

            // 获取角色完整上下文
            const contextResult = await this.getCharacterContext(characterName, true, true);
            if (!contextResult.success) {
                this.log('warning', `角色 ${characterName} 不存在，跳过系统消息生成`);
                return '';
            }

            const { context } = contextResult;
            const { character, state, schedules, psychology } = context;

            let systemMessage = `\n=== 世界树角色系统 ===\n`;
            systemMessage += `角色名称：${character.name}\n`;
            systemMessage += `时间架构：${character.time_structure}\n`;

            if (character.description) {
                systemMessage += `角色描述：${character.description}\n`;
            }

            // 当前状态
            if (state) {
                systemMessage += `\n当前状态：\n`;
                systemMessage += `- 心情：${state.current_mood || '未知'}\n`;
                systemMessage += `- 精力水平：${state.energy_level || 1.0}\n`;
                systemMessage += `- 压力水平：${state.stress_level || 0.0}\n`;

                if (state.active_goals) {
                    try {
                        const goals = JSON.parse(state.active_goals);
                        if (goals.length > 0) {
                            systemMessage += `- 当前目标：${goals.join(', ')}\n`;
                        }
                    } catch (e) {
                        // 忽略JSON解析错误
                    }
                }
            }

            // 当前日程
            if (schedules.length > 0) {
                systemMessage += `\n当前日程：\n`;
                schedules.slice(0, 5).forEach(schedule => {
                    systemMessage += `- ${schedule.title}`;
                    if (schedule.start_time) {
                        systemMessage += ` (${new Date(schedule.start_time).toLocaleString('zh-CN')})`;
                    }
                    if (schedule.description) {
                        systemMessage += `：${schedule.description}`;
                    }
                    systemMessage += `\n`;
                });
            }

            // 最近心理活动
            if (psychology.length > 0) {
                systemMessage += `\n当前心理状态：\n`;
                const latestPsychology = psychology[0];
                systemMessage += `${latestPsychology.content}\n`;
            }

            systemMessage += `\n请严格按照以上角色设定和当前状态进行回应，保持角色的一致性和真实感。\n`;
            systemMessage += `=== 世界树角色系统结束 ===\n`;

            return systemMessage;

        } catch (error) {
            this.log('error', `生成系统消息失败: ${error.message}`);
            return '';
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        if (this.db) {
            await new Promise((resolve) => {
                this.db.close(resolve);
            });
            this.log('info', '数据库连接已关闭');
        }
    }
}

module.exports = WorldTreeSystem;
