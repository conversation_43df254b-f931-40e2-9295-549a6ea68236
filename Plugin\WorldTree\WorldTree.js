// Plugin/WorldTree/WorldTree.js - 世界树角色管理系统
const fs = require('fs').promises;
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const { promisify } = require('util');
const logger = require('../../utils/logger.cjs');

class WorldTreeSystem {
    constructor(pluginDir, config) {
        this.pluginDir = pluginDir;
        this.config = config;
        this.db = null;
        this.dbRun = null;
        this.dbGet = null;
        this.dbAll = null;
        this.isInitialized = false;
        
        // 调试模式
        this.debugMode = config.DebugMode || false;
        
        // AI配置
        this.aiConfig = {
            apiUrl: config.AI_API_URL || 'https://api.openai.com/v1',
            apiKey: config.AI_API_KEY,
            model: config.AI_MODEL || 'gpt-4o-mini',
            psychologyEnabled: config.PSYCHOLOGY_GENERATION_ENABLED !== false,
            maxPsychologyLength: config.MAX_PSYCHOLOGY_LENGTH || 500
        };
        
        // 日程更新间隔
        this.scheduleUpdateInterval = config.SCHEDULE_UPDATE_INTERVAL || 60;
        
        this.log('info', '世界树系统初始化', {
            pluginDir: this.pluginDir,
            debugMode: this.debugMode,
            psychologyEnabled: this.aiConfig.psychologyEnabled
        });
    }

    /**
     * 日志记录方法
     */
    log(level, message, data = null) {
        const logMessage = `[世界树] ${message}`;
        if (data && this.debugMode) {
            logger[level]('世界树系统', logMessage, data);
        } else {
            logger[level]('世界树系统', logMessage);
        }
    }

    /**
     * 初始化数据库
     */
    async initializeDatabase() {
        try {
            this.log('info', '开始初始化数据库...');
            
            // 确保数据目录存在
            const dataDir = path.join(this.pluginDir, 'data');
            await fs.mkdir(dataDir, { recursive: true });
            
            // 数据库路径
            const dbPath = path.join(dataDir, 'world_tree.db');
            this.log('info', `数据库路径: ${dbPath}`);
            
            // 连接数据库
            this.db = new sqlite3.Database(dbPath);
            this.dbRun = promisify(this.db.run.bind(this.db));
            this.dbGet = promisify(this.db.get.bind(this.db));
            this.dbAll = promisify(this.db.all.bind(this.db));
            
            // 创建数据表
            await this.createTables();
            
            this.isInitialized = true;
            this.log('success', '数据库初始化完成');
            
        } catch (error) {
            this.log('error', `数据库初始化失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 创建数据库表
     */
    async createTables() {
        try {
            // Agent基础配置表（不包含用户相关状态）
            await this.dbRun(`
                CREATE TABLE IF NOT EXISTS agent_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_name TEXT UNIQUE NOT NULL,
                    time_structure TEXT NOT NULL,
                    description TEXT,
                    personality_traits TEXT, -- JSON object
                    default_goals TEXT, -- JSON array，默认目标
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // Agent日程表（全局日程，不分用户）
            await this.dbRun(`
                CREATE TABLE IF NOT EXISTS agent_schedules (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_name TEXT NOT NULL,
                    schedule_type TEXT NOT NULL, -- daily, weekly, monthly, event
                    title TEXT NOT NULL,
                    description TEXT,
                    start_time TEXT,
                    end_time TEXT,
                    recurrence_rule TEXT, -- cron-like format
                    priority INTEGER DEFAULT 1, -- 1-5
                    status TEXT DEFAULT 'active', -- active, completed, cancelled
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (agent_name) REFERENCES agent_configs(agent_name)
                )
            `);

            // Agent心理活动记录表（按用户分层）
            await this.dbRun(`
                CREATE TABLE IF NOT EXISTS agent_psychology_activities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_name TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    activity_type TEXT NOT NULL, -- thought, emotion, memory, reaction
                    content TEXT NOT NULL,
                    context_summary TEXT,
                    emotional_context TEXT, -- JSON格式，从AdvancedMemorySystem获取
                    generated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (agent_name) REFERENCES agent_configs(agent_name)
                )
            `);

            // Agent用户关系表（记录Agent与每个用户的个性化状态）
            await this.dbRun(`
                CREATE TABLE IF NOT EXISTS agent_user_relations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_name TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    relationship_type TEXT DEFAULT 'acquaintance', -- 关系类型
                    personal_goals TEXT, -- JSON array，针对该用户的个人目标
                    interaction_count INTEGER DEFAULT 0,
                    last_interaction DATETIME,
                    notes TEXT, -- 关于该用户的备注
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(agent_name, user_id),
                    FOREIGN KEY (agent_name) REFERENCES agent_configs(agent_name)
                )
            `);

            // 创建索引
            await this.dbRun(`CREATE INDEX IF NOT EXISTS idx_schedules_agent ON agent_schedules(agent_name)`);
            await this.dbRun(`CREATE INDEX IF NOT EXISTS idx_schedules_time ON agent_schedules(start_time)`);
            await this.dbRun(`CREATE INDEX IF NOT EXISTS idx_psychology_agent_user ON agent_psychology_activities(agent_name, user_id)`);
            await this.dbRun(`CREATE INDEX IF NOT EXISTS idx_psychology_time ON agent_psychology_activities(generated_at)`);
            await this.dbRun(`CREATE INDEX IF NOT EXISTS idx_relations_agent_user ON agent_user_relations(agent_name, user_id)`);

            this.log('success', '数据库表创建完成');

        } catch (error) {
            this.log('error', `创建数据库表失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 创建或更新Agent配置
     */
    async createOrUpdateAgentConfig(agentName, timeStructure, description = '', personalityTraits = {}, defaultGoals = []) {
        try {
            this.log('info', `创建或更新Agent配置: ${agentName}`);

            // 检查Agent配置是否已存在
            const existing = await this.dbGet(
                'SELECT agent_name FROM agent_configs WHERE agent_name = ?',
                [agentName]
            );

            if (existing) {
                // 更新现有配置
                await this.dbRun(
                    `UPDATE agent_configs
                     SET time_structure = ?, description = ?, personality_traits = ?, default_goals = ?, updated_at = CURRENT_TIMESTAMP
                     WHERE agent_name = ?`,
                    [timeStructure, description, JSON.stringify(personalityTraits), JSON.stringify(defaultGoals), agentName]
                );
                this.log('success', `Agent ${agentName} 配置更新成功`);
                return { success: true, message: `Agent ${agentName} 配置更新成功` };
            } else {
                // 插入新配置
                await this.dbRun(
                    `INSERT INTO agent_configs (agent_name, time_structure, description, personality_traits, default_goals)
                     VALUES (?, ?, ?, ?, ?)`,
                    [agentName, timeStructure, description, JSON.stringify(personalityTraits), JSON.stringify(defaultGoals)]
                );

                this.log('success', `Agent ${agentName} 配置创建成功`);
                return { success: true, message: `Agent ${agentName} 配置创建成功` };
            }

        } catch (error) {
            this.log('error', `创建或更新Agent配置失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取Agent列表
     */
    async getAgents() {
        try {
            const agents = await this.dbAll(`
                SELECT * FROM agent_configs
                ORDER BY created_at DESC
            `);

            return { success: true, agents };

        } catch (error) {
            this.log('error', `获取Agent列表失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 从AdvancedMemorySystem获取用户的实时情感状态
     */
    async getUserEmotionalState(userId, agentName) {
        try {
            if (!global.advancedMemoryPlugin) {
                this.log('warning', 'AdvancedMemorySystem插件未初始化');
                return null;
            }

            // 获取用户的情感状态
            const emotionalState = await global.advancedMemoryPlugin.getCurrentPsychologicalStates(userId, agentName);

            this.log('debug', `获取用户 ${userId} 与 ${agentName} 的情感状态`, emotionalState);

            return emotionalState;

        } catch (error) {
            this.log('error', `获取用户情感状态失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 获取Agent与特定用户的关系信息
     */
    async getAgentUserRelation(agentName, userId) {
        try {
            let relation = await this.dbGet(
                'SELECT * FROM agent_user_relations WHERE agent_name = ? AND user_id = ?',
                [agentName, userId]
            );

            if (!relation) {
                // 创建新的关系记录
                await this.dbRun(
                    `INSERT INTO agent_user_relations (agent_name, user_id, relationship_type, personal_goals)
                     VALUES (?, ?, ?, ?)`,
                    [agentName, userId, 'acquaintance', JSON.stringify([])]
                );

                relation = await this.dbGet(
                    'SELECT * FROM agent_user_relations WHERE agent_name = ? AND user_id = ?',
                    [agentName, userId]
                );
            }

            return relation;

        } catch (error) {
            this.log('error', `获取Agent用户关系失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 获取Agent完整上下文（支持用户维度）
     */
    async getAgentContext(agentName, userId = null, includeSchedule = true, includePsychology = true) {
        try {
            this.log('info', `获取Agent上下文: ${agentName}${userId ? ` (用户: ${userId})` : ''}`);

            // 获取Agent基本信息
            const agent = await this.dbGet(
                'SELECT * FROM agent_configs WHERE agent_name = ?',
                [agentName]
            );

            if (!agent) {
                return { success: false, error: `Agent ${agentName} 不存在` };
            }

            let context = {
                agent,
                schedules: [],
                psychology: [],
                users: []
            };

            // 获取当前日程
            if (includeSchedule) {
                context.schedules = await this.getCurrentSchedules(agentName);
            }

            if (userId) {
                // 获取特定用户的上下文
                const userRelation = await this.getAgentUserRelation(agentName, userId);
                const emotionalState = await this.getUserEmotionalState(userId, agentName);

                context.currentUser = {
                    userId,
                    relation: userRelation,
                    emotionalState
                };

                // 获取与该用户的心理活动
                if (includePsychology) {
                    context.psychology = await this.getRecentPsychology(agentName, userId, 5);
                }
            } else {
                // 获取所有用户的概览
                const allUsers = await this.dbAll(
                    'SELECT * FROM agent_user_relations WHERE agent_name = ? ORDER BY last_interaction DESC LIMIT 10',
                    [agentName]
                );

                context.users = allUsers;

                // 获取最近的心理活动（所有用户）
                if (includePsychology) {
                    context.psychology = await this.getRecentPsychology(agentName, null, 10);
                }
            }

            return { success: true, context };

        } catch (error) {
            this.log('error', `获取Agent上下文失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 获取当前日程
     */
    async getCurrentSchedules(agentName, limit = 10) {
        try {
            const now = new Date().toLocaleString('zh-CN');
            const schedules = await this.dbAll(`
                SELECT * FROM agent_schedules
                WHERE agent_name = ?
                AND status = 'active'
                AND (start_time >= ? OR end_time >= ? OR start_time IS NULL)
                ORDER BY start_time ASC
                LIMIT ?
            `, [agentName, now, now, limit]);

            return schedules;

        } catch (error) {
            this.log('error', `获取当前日程失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 获取最近心理活动
     */
    async getRecentPsychology(agentName, userId = null, limit = 5) {
        try {
            let query, params;

            if (userId) {
                // 获取特定用户的心理活动
                query = `
                    SELECT * FROM agent_psychology_activities
                    WHERE agent_name = ? AND user_id = ?
                    ORDER BY generated_at DESC
                    LIMIT ?
                `;
                params = [agentName, userId, limit];
            } else {
                // 获取所有用户的心理活动
                query = `
                    SELECT * FROM agent_psychology_activities
                    WHERE agent_name = ?
                    ORDER BY generated_at DESC
                    LIMIT ?
                `;
                params = [agentName, limit];
            }

            const psychology = await this.dbAll(query, params);

            return psychology;

        } catch (error) {
            this.log('error', `获取最近心理活动失败: ${error.message}`);
            return [];
        }
    }

    /**
     * 添加日程
     */
    async addSchedule(agentName, scheduleData) {
        try {
            const {
                scheduleType = 'event',
                title,
                description = '',
                startTime,
                endTime,
                recurrenceRule = '',
                priority = 1
            } = scheduleData;

            // 转换为本地时间格式
            const localStartTime = startTime ? new Date(startTime).toLocaleString('zh-CN') : null;
            const localEndTime = endTime ? new Date(endTime).toLocaleString('zh-CN') : null;

            await this.dbRun(`
                INSERT INTO agent_schedules
                (agent_name, schedule_type, title, description, start_time, end_time, recurrence_rule, priority)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [agentName, scheduleType, title, description, localStartTime, localEndTime, recurrenceRule, priority]);

            this.log('success', `为Agent ${agentName} 添加日程: ${title}`);
            return { success: true, message: '日程添加成功' };

        } catch (error) {
            this.log('error', `添加日程失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成心理活动
     */
    async generatePsychology(characterName, conversationContext, memoryData) {
        try {
            if (!this.aiConfig.psychologyEnabled || !this.aiConfig.apiKey) {
                this.log('warning', '心理活动生成未启用或API密钥未配置');
                return { success: false, error: '心理活动生成未启用' };
            }

            this.log('info', `为角色 ${characterName} 生成心理活动`);

            // 获取角色上下文
            const contextResult = await this.getCharacterContext(characterName, true, true);
            if (!contextResult.success) {
                return contextResult;
            }

            const { context } = contextResult;

            // 构建AI请求
            const prompt = this.buildPsychologyPrompt(context, conversationContext, memoryData);

            // 调用AI API
            const psychologyContent = await this.callAIAPI(prompt);

            if (psychologyContent) {
                // 保存心理活动
                await this.savePsychologyActivity(characterName, psychologyContent, conversationContext);

                this.log('success', `角色 ${characterName} 心理活动生成成功`);
                return {
                    success: true,
                    psychology: psychologyContent,
                    message: '心理活动生成成功'
                };
            } else {
                return { success: false, error: 'AI API调用失败' };
            }

        } catch (error) {
            this.log('error', `生成心理活动失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 构建心理活动生成提示词
     */
    buildPsychologyPrompt(context, conversationContext, memoryData) {
        const { character, state, schedules, psychology } = context;

        let prompt = `你是一个专业的心理活动生成器。请为角色"${character.name}"生成当前的心理活动。

角色设定：
- 名称：${character.name}
- 时间架构：${character.time_structure}
- 描述：${character.description || '无'}

当前状态：
- 心情：${state?.current_mood || '未知'}
- 精力水平：${state?.energy_level || 1.0}
- 压力水平：${state?.stress_level || 0.0}

当前日程：
${schedules.length > 0 ? schedules.map(s => `- ${s.title}: ${s.description}`).join('\n') : '无当前日程'}

最近心理活动：
${psychology.length > 0 ? psychology.map(p => `- ${p.activity_type}: ${p.content}`).join('\n') : '无历史记录'}

对话上下文：
${conversationContext || '无当前对话'}

记忆数据：
${memoryData ? JSON.stringify(memoryData, null, 2) : '无记忆数据'}

请生成一段简洁的心理活动描述（不超过${this.aiConfig.maxPsychologyLength}字），包括：
1. 当前的思考状态
2. 情绪反应
3. 对当前情况的内心感受
4. 可能的行为倾向

要求：
- 符合角色设定和时间架构
- 体现角色的个性特点
- 与当前对话和记忆相关
- 语言自然流畅，第一人称视角
- 不要包含任何格式标记，直接输出心理活动内容`;

        return prompt;
    }

    /**
     * 调用AI API
     */
    async callAIAPI(prompt) {
        try {
            const fetch = (await import('node-fetch')).default;

            const response = await fetch(`${this.aiConfig.apiUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.aiConfig.apiKey}`
                },
                body: JSON.stringify({
                    model: this.aiConfig.model,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: Math.min(this.aiConfig.maxPsychologyLength * 2, 1000),
                    temperature: 0.7
                })
            });

            if (!response.ok) {
                throw new Error(`AI API请求失败: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            const content = data.choices?.[0]?.message?.content;

            if (!content) {
                throw new Error('AI API返回内容为空');
            }

            return content.trim();

        } catch (error) {
            this.log('error', `AI API调用失败: ${error.message}`);
            return null;
        }
    }

    /**
     * 保存心理活动
     */
    async savePsychologyActivity(characterName, content, contextSummary) {
        try {
            await this.dbRun(`
                INSERT INTO psychology_activities
                (character_name, activity_type, content, context_summary)
                VALUES (?, ?, ?, ?)
            `, [characterName, 'thought', content, contextSummary || '']);

            this.log('info', `保存心理活动: ${characterName}`);

        } catch (error) {
            this.log('error', `保存心理活动失败: ${error.message}`);
        }
    }

    /**
     * 更新角色状态
     */
    async updateCharacterState(characterName, stateData) {
        try {
            const {
                currentMood,
                energyLevel,
                stressLevel,
                activeGoals,
                personalityTraits
            } = stateData;

            const updates = [];
            const values = [];

            if (currentMood !== undefined) {
                updates.push('current_mood = ?');
                values.push(currentMood);
            }
            if (energyLevel !== undefined) {
                updates.push('energy_level = ?');
                values.push(energyLevel);
            }
            if (stressLevel !== undefined) {
                updates.push('stress_level = ?');
                values.push(stressLevel);
            }
            if (activeGoals !== undefined) {
                updates.push('active_goals = ?');
                values.push(JSON.stringify(activeGoals));
            }
            if (personalityTraits !== undefined) {
                updates.push('personality_traits = ?');
                values.push(JSON.stringify(personalityTraits));
            }

            updates.push('last_interaction = CURRENT_TIMESTAMP');
            updates.push('updated_at = CURRENT_TIMESTAMP');
            values.push(characterName);

            await this.dbRun(`
                UPDATE character_states
                SET ${updates.join(', ')}
                WHERE character_name = ?
            `, values);

            this.log('success', `角色状态更新成功: ${characterName}`);
            return { success: true, message: '角色状态更新成功' };

        } catch (error) {
            this.log('error', `更新角色状态失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 生成系统消息内容（支持用户维度）
     */
    async generateSystemMessage(agentName, userId = null) {
        try {
            this.log('info', `生成系统消息: ${agentName}${userId ? ` (用户: ${userId})` : ''}`);

            // 获取Agent完整上下文
            const contextResult = await this.getAgentContext(agentName, userId, true, true);
            if (!contextResult.success) {
                this.log('warning', `Agent ${agentName} 不存在，跳过系统消息生成`);
                return '';
            }

            const { context } = contextResult;
            const { agent, schedules, psychology, currentUser, users } = context;

            let systemMessage = `\n=== 世界树角色系统 ===\n`;
            systemMessage += `角色名称：${agent.agent_name}\n`;
            systemMessage += `时间架构：${agent.time_structure}\n`;

            if (agent.description) {
                systemMessage += `角色描述：${agent.description}\n`;
            }

            // 个性特征
            if (agent.personality_traits) {
                try {
                    const traits = JSON.parse(agent.personality_traits);
                    if (Object.keys(traits).length > 0) {
                        systemMessage += `个性特征：${JSON.stringify(traits, null, 2)}\n`;
                    }
                } catch (e) {
                    // 忽略JSON解析错误
                }
            }

            // 当前用户相关信息
            if (currentUser && currentUser.emotionalState) {
                const { emotionalState, relation } = currentUser;

                systemMessage += `\n当前用户状态 (${userId})：\n`;

                // 从AdvancedMemorySystem获取的实时情感状态
                if (emotionalState.emotion) {
                    systemMessage += `- 情绪状态：${emotionalState.emotion_type || '未知'} (${emotionalState.emotion || 0})\n`;
                }
                if (emotionalState.stress !== undefined) {
                    systemMessage += `- 压力水平：${emotionalState.stress || 0}\n`;
                }
                if (emotionalState.affinity !== undefined) {
                    systemMessage += `- 好感度：${emotionalState.affinity || 0}\n`;
                }

                // 关系信息
                if (relation) {
                    systemMessage += `- 关系类型：${relation.relationship_type || '熟人'}\n`;
                    systemMessage += `- 交互次数：${relation.interaction_count || 0}\n`;

                    if (relation.personal_goals) {
                        try {
                            const goals = JSON.parse(relation.personal_goals);
                            if (goals.length > 0) {
                                systemMessage += `- 针对该用户的目标：${goals.join(', ')}\n`;
                            }
                        } catch (e) {
                            // 忽略JSON解析错误
                        }
                    }
                }
            }

            // 当前日程
            if (schedules.length > 0) {
                systemMessage += `\n当前日程：\n`;
                schedules.slice(0, 5).forEach(schedule => {
                    systemMessage += `- ${schedule.title}`;
                    if (schedule.start_time) {
                        systemMessage += ` (${schedule.start_time})`;
                    }
                    if (schedule.description) {
                        systemMessage += `：${schedule.description}`;
                    }
                    systemMessage += `\n`;
                });
            }

            // 最近心理活动
            if (psychology.length > 0) {
                systemMessage += `\n最近心理活动：\n`;
                psychology.slice(0, 3).forEach(psych => {
                    systemMessage += `- [${psych.user_id || '全局'}] ${psych.content}\n`;
                });
            }

            // 多用户概览（如果没有指定特定用户）
            if (!userId && users.length > 0) {
                systemMessage += `\n用户关系概览：\n`;
                users.slice(0, 5).forEach(user => {
                    systemMessage += `- ${user.user_id}: ${user.relationship_type} (交互${user.interaction_count}次)\n`;
                });
            }

            systemMessage += `\n请严格按照以上角色设定和当前状态进行回应，保持角色的一致性和真实感。\n`;
            systemMessage += `特别注意：利用从情感记忆系统获取的实时用户状态信息，提供更加个性化和贴心的回应。\n`;
            systemMessage += `=== 世界树角色系统结束 ===\n`;

            return systemMessage;

        } catch (error) {
            this.log('error', `生成系统消息失败: ${error.message}`);
            return '';
        }
    }

    /**
     * 关闭数据库连接
     */
    async close() {
        if (this.db) {
            await new Promise((resolve) => {
                this.db.close(resolve);
            });
            this.log('info', '数据库连接已关闭');
        }
    }
}

module.exports = WorldTreeSystem;
