{"manifestVersion": "1.0.0", "name": "WorldTree", "displayName": "世界树角色管理系统", "version": "1.0.0", "description": "管理角色时间架构、日程表和心理活动的综合系统，支持动态system消息注入和AI心理状态生成", "author": "VCPToolBox Team", "pluginType": "service", "entryPoint": {"type": "nodejs", "script": "PluginInterface.js"}, "communication": {"protocol": "direct", "timeout": 30000}, "configSchema": {"DebugMode": {"type": "boolean", "description": "是否启用调试模式", "required": false, "default": false}, "AI_API_URL": {"type": "string", "description": "AI API 基础URL", "required": true, "default": "https://api.openai.com/v1"}, "AI_API_KEY": {"type": "string", "description": "AI API 密钥", "required": true}, "AI_MODEL": {"type": "string", "description": "AI 模型名称", "required": false, "default": "gpt-4o-mini"}, "PSYCHOLOGY_GENERATION_ENABLED": {"type": "boolean", "description": "是否启用心理活动生成", "required": false, "default": true}, "MAX_PSYCHOLOGY_LENGTH": {"type": "integer", "description": "心理活动最大长度", "required": false, "default": 500}, "SCHEDULE_UPDATE_INTERVAL": {"type": "integer", "description": "日程表更新间隔（分钟）", "required": false, "default": 60}}, "capabilities": {"systemPromptPlaceholders": [{"placeholder": "{{WorldTreeCharacterContext}}", "description": "根据角色名称动态注入的角色时间架构、日程表和心理活动内容"}], "invocationCommands": [{"commandIdentifier": "WorldTreeManage", "description": "管理世界树角色系统，包括创建角色、设置时间架构、管理日程表等功能", "example": "<<<[TOOL_REQUEST]>>>\ntool_name:「始」WorldTreeManage「末」,\naction:「始」create_character「末」,\ncharacter_name:「始」雨安安「末」,\ntime_structure:「始」现代都市设定，2024年12月「末」\n<<<[END_TOOL_REQUEST]>>>"}]}, "database": {"type": "sqlite3", "file": "data/world_tree.db"}, "api": {"getCharacterContext": {"description": "获取指定角色的完整上下文信息", "parameters": ["<PERSON><PERSON><PERSON>", "includeSchedule", "includePsychology"]}, "createCharacter": {"description": "创建新角色", "parameters": ["<PERSON><PERSON><PERSON>", "timeStructure", "description"]}, "updateSchedule": {"description": "更新角色日程表", "parameters": ["<PERSON><PERSON><PERSON>", "scheduleData"]}, "generatePsychology": {"description": "生成角色心理活动", "parameters": ["<PERSON><PERSON><PERSON>", "conversationContext", "memoryData"]}}}