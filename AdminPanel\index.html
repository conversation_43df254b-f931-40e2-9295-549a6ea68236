<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sentra 管理面板</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Three.js 和 MMD 相关库 - 本地文件 (r106) -->
    <script src="libs/threejs/three.min.js"></script>
    <script src="libs/threejs/mmdparser.min.js"></script>
    <script src="libs/threejs/TGALoader.js"></script>
    <script src="libs/threejs/MMDLoader.js"></script>
    <script src="libs/threejs/MMDAnimationHelper.js"></script>
    <script src="libs/threejs/OutlineEffect.js"></script>
    <script src="libs/threejs/OrbitControls.js"></script>
    <script src="libs/threejs/CCDIKSolver.js"></script>
    <script src="libs/threejs/MMDPhysics.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'anime-pink': '#E91E63',
                        'anime-purple': '#8E24AA',
                        'anime-blue': '#1976D2',
                        'anime-green': '#388E3C',
                        'anime-orange': '#F57C00',
                        'anime-red': '#D32F2F',
                        'anime-dark': '#263238',
                        'anime-light': '#ECEFF1'
                    },
                    fontFamily: {
                        'anime': ['"Microsoft YaHei"', '"PingFang SC"', '"Hiragino Sans GB"', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 3s ease-in-out infinite',
                        'pulse-slow': 'pulse 2s infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-in': 'slideIn 0.3s ease-out'
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideIn: {
                            '0%': { transform: 'translateX(-20px)', opacity: '0' },
                            '100%': { transform: 'translateX(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* 自定义样式补充 */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .anime-gradient {
            background: linear-gradient(135deg, #FF6B9D 0%, #9B59B6 50%, #5DADE2 100%);
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #FF6B9D;
        }
        input:focus + .slider {
            box-shadow: 0 0 1px #FF6B9D;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
        
        /* 现代化开关控件样式 */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 24px;
        }

        .toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-label {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #e5e7eb;
            transition: all 0.3s ease;
            border-radius: 12px;
            border: 1px solid #d1d5db;
        }

        .toggle-label:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: all 0.3s ease;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-input:checked + .toggle-label {
            background: linear-gradient(135deg, #10b981, #059669);
            border-color: #059669;
        }

        .toggle-input:focus + .toggle-label {
            box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
        }

        .toggle-input:checked + .toggle-label:before {
            transform: translateX(24px);
        }

        .toggle-input:disabled + .toggle-label {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        /* 迷你开关控件样式 - 科学算法配置专用 */
        .mini-toggle-switch {
            position: relative;
            display: inline-block;
            width: 36px;
            height: 18px;
        }

        .mini-toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .mini-toggle-label {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #f3f4f6;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 9px;
            border: 1px solid #d1d5db;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .mini-toggle-label:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 1px;
            bottom: 1px;
            background: linear-gradient(135deg, #ffffff, #f9fafb);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 50%;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .mini-toggle-input:checked + .mini-toggle-label {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            border-color: #7c3aed;
            box-shadow: 0 0 0 1px rgba(139, 92, 246, 0.1);
        }

        .mini-toggle-input:checked + .mini-toggle-label.bg-purple-200 {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .mini-toggle-input:checked + .mini-toggle-label.bg-blue-200 {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
        }

        .mini-toggle-input:checked + .mini-toggle-label.bg-green-200 {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .mini-toggle-input:checked + .mini-toggle-label.bg-orange-200 {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .mini-toggle-input:focus + .mini-toggle-label {
            box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
        }

        .mini-toggle-input:checked + .mini-toggle-label:before {
            transform: translateX(18px);
            background: linear-gradient(135deg, #ffffff, #fefefe);
        }

        .mini-toggle-input:disabled + .mini-toggle-label {
            opacity: 0.5;
            cursor: not-allowed;
        }

        /* 科学算法卡片的hover效果优化 */
        .scientific-algorithm-card:hover .mini-toggle-label {
            border-color: #a855f7;
        }

        /* 颜色主题背景 */
        .bg-purple-25 { background-color: rgba(139, 92, 246, 0.05); }
        .bg-blue-25 { background-color: rgba(59, 130, 246, 0.05); }
        .bg-green-25 { background-color: rgba(16, 185, 129, 0.05); }
        .bg-orange-25 { background-color: rgba(245, 158, 11, 0.05); }
        
        /* 配置状态徽章样式 */
        .config-status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .config-status-badge.configured {
            background-color: rgba(34, 197, 94, 0.1);
            color: rgb(22, 163, 74);
        }
        
        .config-status-badge.inherited {
            background-color: rgba(59, 130, 246, 0.1);
            color: rgb(37, 99, 235);
        }
        
        .config-status-badge.default {
            background-color: rgba(168, 85, 247, 0.1);
            color: rgb(147, 51, 234);
        }
        
        .config-status-badge.required {
            background-color: rgba(239, 68, 68, 0.1);
            color: rgb(220, 38, 38);
        }
        
        .config-status-badge.empty {
            background-color: rgba(156, 163, 175, 0.1);
            color: rgb(107, 114, 128);
        }
        
        /* 活跃状态样式 */
        .active {
            background: linear-gradient(135deg, #FF6B9D 0%, #9B59B6 100%) !important;
            color: white !important;
        }
        
        .active-section {
            display: block !important;
        }
        
        /* 插件配置卡片优化 */
        .plugin-config-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.9) 100%);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(0, 0, 0, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            margin-bottom: 1rem;
        }
        
        .plugin-config-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            border-color: rgba(255, 107, 157, 0.2);
        }
        
        /* 表单输入框现代化样式 */
        .modern-input {
            background: rgba(255, 255, 255, 0.9);
            border: 1.5px solid rgba(0, 0, 0, 0.12);
            transition: all 0.3s ease;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .modern-input:focus {
            background: rgba(255, 255, 255, 1);
            border-color: #ff6b9d;
            box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.08);
            transform: translateY(-1px);
            outline: none;
        }
        
        .modern-input::placeholder {
            color: rgba(0, 0, 0, 0.4);
        }
        
        /* 配置项标题和描述优化 */
        .config-title {
            color: #2d3748;
            font-weight: 600;
        }
        
        /* 优化滚动条样式 */
        .custom-scrollbar,
        .main-content-scroll {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
        }
        
        .custom-scrollbar::-webkit-scrollbar,
        .main-content-scroll::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-track,
        .main-content-scroll::-webkit-scrollbar-track {
            background: rgba(243, 244, 246, 0.5);
            border-radius: 4px;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb,
        .main-content-scroll::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.8);
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .custom-scrollbar::-webkit-scrollbar-thumb:hover,
        .main-content-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(107, 114, 128, 0.9);
        }
        
        /* 日记卡片增强样式 */
        .diary-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }
        
        .diary-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
            border-color: rgba(255, 107, 157, 0.2);
        }
        
        /* Agent文件卡片样式 */
        .agent-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.95) 100%);
            border: 1px solid rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .agent-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #FF6B9D, #9B59B6, #5DADE2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .agent-card:hover::before {
            opacity: 1;
        }
        
        .agent-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        
        /* 日志查看器优化 */
        .log-viewer {
            background: #1a1a1a;
            border: 1px solid #333;
            position: relative;
        }
        
        .log-line {
            padding: 2px 0;
            border-left: 3px solid transparent;
            transition: all 0.2s ease;
        }
        
        .log-line:hover {
            background: rgba(255, 255, 255, 0.05);
            border-left-color: #FF6B9D;
        }
        
        .log-line.error {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        
        .log-line.warning {
            color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }
        
        .log-line.info {
            color: #3b82f6;
        }
        
        .log-line.success {
            color: #10b981;
        }
        
        /* 响应式布局优化 */
        @media (max-width: 1024px) {
            .diary-grid {
                grid-template-columns: 1fr !important;
            }
            
            .agent-grid {
                grid-template-columns: 1fr !important;
            }
        }
        
        @media (max-width: 768px) {
            .config-sidebar {
                transform: translateX(-100%);
                position: fixed;
                z-index: 40;
                transition: transform 0.3s ease;
            }
            
            .config-sidebar.mobile-open {
                transform: translateX(0);
            }
        }
        
        /* 加载动画 */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .config-title {
            font-size: 16px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }
        
        .config-description {
            background: rgba(59, 130, 246, 0.05);
            border-left: 3px solid #3b82f6;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 13px;
            line-height: 1.6;
            color: #4b5563;
        }
        
        /* 配置类型徽章优化 */
        .config-type-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            margin-left: 8px;
        }
        
        .config-type-badge.boolean {
            background: rgba(34, 197, 94, 0.1);
            color: #059669;
        }
        
        .config-type-badge.integer {
            background: rgba(59, 130, 246, 0.1);
            color: #2563eb;
        }
        
        .config-type-badge.string {
            background: rgba(168, 85, 247, 0.1);
            color: #7c3aed;
        }
        
        /* 开关容器优化 */
        .switch-container {
            background: rgba(255, 255, 255, 0.9);
            border: 1.5px solid rgba(0, 0, 0, 0.08);
            backdrop-filter: blur(8px);
        }
        
        /* 删除按钮优化 */
        .delete-config-btn {
            background: rgba(239, 68, 68, 0.1);
            color: #dc2626;
            border: 1px solid rgba(239, 68, 68, 0.2);
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .delete-config-btn:hover {
            background: #dc2626;
            color: white;
            transform: scale(1.05);
        }
        
        /* 表单行动按钮组优化 */
        .form-actions {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            padding: 24px;
            border-radius: 0 0 12px 12px;
            margin-top: 24px;
            display: flex;
            gap: 12px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        /* 通用按钮样式 */
        button[type="submit"], .save-config-btn, .add-config-btn {
            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        button[type="submit"]:hover, .save-config-btn:hover, .add-config-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(25, 118, 210, 0.4);
        }
        
        button[type="submit"]:active, .save-config-btn:active, .add-config-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
        }
        
        .add-config-btn {
            background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
            box-shadow: 0 2px 8px rgba(56, 142, 60, 0.3);
        }
        
        .add-config-btn:hover {
            box-shadow: 0 4px 16px rgba(56, 142, 60, 0.4);
        }
        
        /* 命令描述文本域样式 */
        .command-description-edit, textarea {
            background: rgba(255, 255, 255, 0.9);
            border: 1.5px solid rgba(0, 0, 0, 0.12);
            border-radius: 8px;
            padding: 16px;
            font-family: 'JetBrains Mono', 'Consolas', monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            transition: all 0.3s ease;
        }
        
        .command-description-edit:focus, textarea:focus {
            border-color: #1976d2;
            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
            background: rgba(255, 255, 255, 1);
            outline: none;
        }
        
        /* 标题层级样式 */
        h3.section-title {
            color: #263238;
            font-size: 20px;
            font-weight: 700;
            margin: 24px 0 16px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #e3f2fd;
            position: relative;
        }
        
        h3.section-title::before {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, #1976d2, #42a5f5);
        }
        
        h4.subsection-title {
            color: #37474f;
            font-size: 16px;
            font-weight: 600;
            margin: 16px 0 12px 0;
            display: flex;
            align-items: center;
        }
        
        h4.subsection-title::before {
            content: '';
            width: 4px;
            height: 16px;
            background: #1976d2;
            margin-right: 8px;
            border-radius: 2px;
        }
        
        /* 提示文本样式 */
        .help-text, .description-text {
            color: #546e7a !important;
            font-size: 13px;
            line-height: 1.5;
            margin-top: 4px;
            font-style: italic;
        }
        
        /* 命令编辑区域样式 */
        .invocation-commands-section {
            background: rgba(255, 255, 255, 0.6);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(0, 0, 0, 0.08);
        }
        
        .command-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }
        
        .command-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        /* 滚动条区域分离 */
        .main-content-scroll {
            overflow-y: auto;
            max-height: calc(100vh - 160px);
        }
        
        .sidebar-scroll {
            overflow-y: auto;
            max-height: calc(100vh - 160px);
        }
        
        /* 深色主题 */
        [data-theme="dark"] {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }
        
        /* 深色主题按钮样式 */
        [data-theme="dark"] button[type="submit"], 
        [data-theme="dark"] .save-config-btn, 
        [data-theme="dark"] .add-config-btn {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
            box-shadow: 0 2px 8px rgba(30, 64, 175, 0.4) !important;
            color: #f1f5f9 !important;
        }
        
        [data-theme="dark"] .add-config-btn {
            background: linear-gradient(135deg, #16a34a 0%, #15803d 100%) !important;
            box-shadow: 0 2px 8px rgba(22, 163, 74, 0.4) !important;
        }
        
        /* 深色主题文本域样式 */
        [data-theme="dark"] .command-description-edit, 
        [data-theme="dark"] textarea {
            background: rgba(30, 41, 59, 0.7) !important;
            border-color: rgba(100, 116, 139, 0.3) !important;
            color: #f1f5f9 !important;
        }
        
        [data-theme="dark"] .command-description-edit:focus, 
        [data-theme="dark"] textarea:focus {
            border-color: #1e40af !important;
            box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.2) !important;
            background: rgba(30, 41, 59, 0.9) !important;
        }
        
        /* 深色主题标题样式 */
        [data-theme="dark"] h3.section-title {
            color: #f1f5f9 !important;
            border-bottom-color: rgba(30, 64, 175, 0.3) !important;
        }
        
        [data-theme="dark"] h4.subsection-title {
            color: #e2e8f0 !important;
        }
        
        [data-theme="dark"] h4.subsection-title::before {
            background: #1e40af !important;
        }
        
        /* 深色主题提示文本 */
        [data-theme="dark"] .help-text, 
        [data-theme="dark"] .description-text {
            color: #94a3b8 !important;
        }
        
        /* 深色主题命令区域 */
        [data-theme="dark"] .invocation-commands-section {
            background: rgba(30, 41, 59, 0.4) !important;
            border-color: rgba(100, 116, 139, 0.2) !important;
        }
        
        [data-theme="dark"] .command-item {
            background: rgba(30, 41, 59, 0.6) !important;
            border-color: rgba(100, 116, 139, 0.2) !important;
        }
        
        [data-theme="dark"] .command-item:hover {
            background: rgba(30, 41, 59, 0.8) !important;
        }
        
        [data-theme="dark"] .bg-white {
            background-color: #1e293b !important;
            color: #f1f5f9 !important;
        }
        
        [data-theme="dark"] .bg-gray-50 {
            background-color: #0f172a !important;
        }
        
        [data-theme="dark"] .text-gray-700 {
            color: #f1f5f9 !important;
        }
        
        [data-theme="dark"] .text-gray-600 {
            color: #cbd5e1 !important;
        }
        
        [data-theme="dark"] .border-gray-200 {
            border-color: #334155 !important;
        }
        
        [data-theme="dark"] .border-gray-300 {
            border-color: #475569 !important;
        }
        
        /* 深色主题下的配置卡片 */
        [data-theme="dark"] .plugin-config-card {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%);
            border-color: rgba(148, 163, 184, 0.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }
        
        [data-theme="dark"] .plugin-config-card:hover {
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            border-color: rgba(255, 107, 157, 0.3);
        }
        
        /* 深色主题下的输入框 */
        [data-theme="dark"] .modern-input {
            background: rgba(30, 41, 59, 0.7) !important;
            border-color: rgba(148, 163, 184, 0.2) !important;
            color: #f1f5f9 !important;
        }
        
        [data-theme="dark"] .modern-input:focus {
            background: rgba(30, 41, 59, 0.9) !important;
            border-color: #ff6b9d !important;
            box-shadow: 0 0 0 3px rgba(255, 107, 157, 0.15) !important;
        }
        
        [data-theme="dark"] .modern-input::placeholder {
            color: rgba(203, 213, 225, 0.5) !important;
        }
        
        /* 深色主题下的标题和描述 */
        [data-theme="dark"] .config-title {
            color: #f1f5f9 !important;
        }
        
        [data-theme="dark"] .config-description {
            background: rgba(59, 130, 246, 0.1) !important;
            border-left-color: #60a5fa !important;
            color: #cbd5e1 !important;
        }
        
        /* 深色主题下的开关按钮 */
        [data-theme="dark"] .switch-container {
            background: rgba(30, 41, 59, 0.5) !important;
            border-color: rgba(148, 163, 184, 0.2) !important;
        }
        
        /* 深色主题下的按钮 */
        [data-theme="dark"] button:not(.bg-gradient-to-r):not(.bg-anime-green):not(.bg-anime-red):not(.bg-anime-blue) {
            background: rgba(30, 41, 59, 0.8) !important;
            color: #f1f5f9 !important;
            border-color: rgba(148, 163, 184, 0.2) !important;
        }
        
        /* 深色主题下的配置状态徽章 */
        [data-theme="dark"] .config-status-badge.configured {
            background-color: rgba(34, 197, 94, 0.15) !important;
            color: #4ade80 !important;
        }
        
        [data-theme="dark"] .config-status-badge.inherited {
            background-color: rgba(59, 130, 246, 0.15) !important;
            color: #60a5fa !important;
        }
        
        [data-theme="dark"] .config-status-badge.default {
            background-color: rgba(168, 85, 247, 0.15) !important;
            color: #a78bfa !important;
        }
        
        [data-theme="dark"] .config-status-badge.required {
            background-color: rgba(239, 68, 68, 0.15) !important;
            color: #f87171 !important;
        }
        
        [data-theme="dark"] .config-status-badge.empty {
            background-color: rgba(148, 163, 184, 0.15) !important;
            color: #94a3b8 !important;
        }
        
        /* 深色主题下的配置类型徽章 */
        [data-theme="dark"] .config-type-badge.boolean {
            background: rgba(34, 197, 94, 0.15) !important;
            color: #4ade80 !important;
        }
        
        [data-theme="dark"] .config-type-badge.integer {
            background: rgba(59, 130, 246, 0.15) !important;
            color: #60a5fa !important;
        }
        
        [data-theme="dark"] .config-type-badge.string {
            background: rgba(168, 85, 247, 0.15) !important;
            color: #a78bfa !important;
        }
        
        /* 深色主题下的删除按钮 */
        [data-theme="dark"] .delete-config-btn {
            background: rgba(239, 68, 68, 0.15) !important;
            color: #f87171 !important;
            border-color: rgba(239, 68, 68, 0.3) !important;
        }
        
        [data-theme="dark"] .delete-config-btn:hover {
            background: #dc2626 !important;
            color: white !important;
        }
        
        /* 深色主题下的表单操作区域 */
        [data-theme="dark"] .form-actions {
            background: rgba(30, 41, 59, 0.8) !important;
            border-top-color: rgba(148, 163, 184, 0.2) !important;
        }
        
        /* 深色主题下的开关值显示 */
        [data-theme="dark"] .switch-container .text-gray-700 {
            color: #cbd5e1 !important;
        }
        
        [data-theme="dark"] .switch-container .text-green-700 {
            color: #4ade80 !important;
        }
        
        /* 平滑过渡 */
        * {
            transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
        }
        
        /* 自定义滚动条样式 */
        .main-content-scroll::-webkit-scrollbar,
        .sidebar-scroll::-webkit-scrollbar {
            width: 8px;
        }
        
        .main-content-scroll::-webkit-scrollbar-track,
        .sidebar-scroll::-webkit-scrollbar-track {
            background: rgba(248, 250, 252, 0.8);
            border-radius: 4px;
        }
        
        .main-content-scroll::-webkit-scrollbar-thumb,
        .sidebar-scroll::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #94a3b8, #64748b);
            border-radius: 4px;
            transition: background 0.3s ease;
        }
        
        .main-content-scroll::-webkit-scrollbar-thumb:hover,
        .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #64748b, #475569);
        }
        
        /* 深色主题滚动条 */
        [data-theme="dark"] .main-content-scroll::-webkit-scrollbar-track,
        [data-theme="dark"] .sidebar-scroll::-webkit-scrollbar-track {
            background: rgba(15, 23, 42, 0.8);
        }
        
        [data-theme="dark"] .main-content-scroll::-webkit-scrollbar-thumb,
        [data-theme="dark"] .sidebar-scroll::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #475569, #334155);
        }
        
        [data-theme="dark"] .main-content-scroll::-webkit-scrollbar-thumb:hover,
        [data-theme="dark"] .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #64748b, #475569);
        }
        
        /* 自定义动画 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes bounce-slow {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
        }
        
        @keyframes pulse-slow {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .animate-float {
            animation: float 3s ease-in-out infinite;
        }
        
        .animate-bounce-slow {
            animation: bounce-slow 0.5s ease-in-out;
        }
        
        .animate-pulse-slow {
            animation: pulse-slow 2s ease-in-out infinite;
        }
        
        /* 状态徽章样式 */
        .config-status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .config-status-badge.configured {
            background-color: #dcfce7;
            color: #16a34a;
        }
        
        .config-status-badge.inherited {
            background-color: #dbeafe;
            color: #2563eb;
        }
        
        .config-status-badge.default {
            background-color: #f3f4f6;
            color: #6b7280;
        }
        
        .config-status-badge.required {
            background-color: #fef2f2;
            color: #dc2626;
        }
        
        .config-status-badge.empty {
            background-color: #f9fafb;
            color: #9ca3af;
        }
        
        /* 活跃导航项 */
        #plugin-nav a.active {
            background: linear-gradient(to right, #ff6b9d, #9b59b6) !important;
            color: white !important;
        }
        
        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #ff6b9d, #9b59b6);
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #e91e63, #8e24aa);
        }
        
        [data-theme="dark"] ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }
        
        /* 日记卡片优化 */
        .note-card-modern {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        .note-card-modern:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
        
        [data-theme="dark"] .note-card-modern {
            background: linear-gradient(135deg, rgba(45, 55, 72, 0.95) 0%, rgba(26, 32, 44, 0.95) 100%);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        /* 加载动画优化 */
        .loading-dots {
            display: inline-block;
        }
        
        .loading-dots::after {
            content: '';
            animation: loading-dots 1.5s infinite;
        }
        
        @keyframes loading-dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
        
        /* 响应式调整 */
        @media (max-width: 1024px) {
            .w-80 {
                width: 16rem;
            }
        }
        
        @media (max-width: 768px) {
            .w-80 {
                display: none;
            }
            
            .plugin-config-card {
                margin: 0.5rem;
                padding: 1rem;
            }
        }
        
        /* Live2D 模型样式 */
        #live2d-widget {
            transition: all 0.3s ease;
            user-select: none;
        }
        
        #live2d-widget.hidden {
            opacity: 0;
            transform: translateY(20px);
            pointer-events: none;
        }
        
        /* 旧的Live2D控制面板样式已移除 - 现在由Live2DManager处理 */
        
        #live2d-controls button:active {
            transform: scale(0.95);
        }
        
        /* 深色主题下的Live2D控制面板 */
        [data-theme="dark"] #live2d-controls {
            background: rgba(30, 41, 59, 0.9);
            border-color: rgba(100, 116, 139, 0.3);
        }
        
        /* Live2D 模型选择器样式 */
        #live2d-model-selector {
            backdrop-filter: blur(8px);
        }
        
        .model-card {
            transition: all 0.2s ease;
        }
        
        .model-card:hover {
            transform: translateY(-2px);
        }
        
        /* 深色主题下的模型选择器 */
        [data-theme="dark"] #live2d-model-selector .bg-white {
            background: rgba(30, 41, 59, 0.95) !important;
            color: #f1f5f9;
        }
        
        [data-theme="dark"] .model-card {
            background: rgba(51, 65, 85, 0.8) !important;
            color: #f1f5f9;
        }
        
        [data-theme="dark"] .model-card:hover {
            background: rgba(71, 85, 105, 0.9) !important;
        }
        
        [data-theme="dark"] .model-card.ring-anime-pink {
            background: rgba(139, 92, 246, 0.3) !important;
        }
        
        /* 消息气泡样式 */
        #live2d-message-bubble {
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        [data-theme="dark"] #live2d-message-bubble {
            background: rgba(30, 41, 59, 0.9) !important;
            color: #f1f5f9;
            border-color: rgba(100, 116, 139, 0.3);
        }
        
        /* Live2D设置面板样式 */
        #live2d-settings-panel {
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s ease-out;
        }
        
        #live2d-settings-panel h3 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        #live2d-settings-panel input[type="radio"] {
            accent-color: #667eea;
        }
        
        #live2d-settings-panel button {
            transition: all 0.2s ease;
            font-weight: 500;
        }
        
        #live2d-settings-panel button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        #live2d-settings-panel button:active {
            transform: translateY(0);
        }
        
        /* 性能优化提示样式 */
        #low-end-device-notice {
            animation: slideInRight 0.5s ease-out;
        }
        
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        /* 深色主题下的设置面板 */
        [data-theme="dark"] #live2d-settings-panel {
            background: rgba(30, 30, 30, 0.95);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        [data-theme="dark"] #live2d-settings-panel h3 {
            color: #e2e8f0;
        }
        
        [data-theme="dark"] #live2d-settings-panel .bg-blue-50 {
            background: rgba(59, 130, 246, 0.1);
        }
        
        [data-theme="dark"] #live2d-settings-panel .text-blue-800 {
            color: #93c5fd;
        }
        
        [data-theme="dark"] #live2d-settings-panel .text-blue-700 {
            color: #bfdbfe;
        }
        
        /* Live2D禁用通知样式 */
        #live2d-disabled-notice {
            animation: slideInFromRight 0.5s ease-out;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(245, 158, 11, 0.3);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        #live2d-disabled-notice button {
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-weight: 500;
        }
        
        #live2d-disabled-notice button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        #live2d-disabled-notice button:active {
            transform: translateY(0);
        }
        
        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        [data-theme="dark"] #live2d-disabled-notice {
            background: linear-gradient(135deg, rgba(92, 62, 9, 0.9) 0%, rgba(120, 83, 14, 0.9) 100%);
            border-color: rgba(245, 158, 11, 0.4);
        }
        
        /* 响应式适配 */
        @media (max-width: 768px) {
            #live2d-settings-panel {
                left: 1rem;
                right: 1rem;
                top: 1rem;
                min-width: auto;
                max-width: none;
            }
            
            #low-end-device-notice,
            #live2d-disabled-notice {
                left: 1rem;
                right: 1rem;
                bottom: 1rem;
                max-width: none;
                font-size: 12px;
            }
        }
        
        /* 命令描述文本框优化 */
        .command-description-edit {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.98) 100%);
            border: 2px solid rgba(99, 102, 241, 0.2);
            border-radius: 12px;
            padding: 16px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #374151;
            resize: vertical;
            min-height: 120px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(8px);
        }
        
        .command-description-edit:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 
                0 0 0 3px rgba(99, 102, 241, 0.1),
                0 4px 16px rgba(99, 102, 241, 0.15),
                0 2px 8px rgba(0, 0, 0, 0.1);
            background: rgba(255, 255, 255, 0.98);
            transform: translateY(-1px);
        }
        
        .command-description-edit:hover {
            border-color: rgba(99, 102, 241, 0.3);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.12);
        }
        
        .command-description-edit::placeholder {
            color: #9ca3af;
            font-style: italic;
        }
        
        /* 深色主题下的命令描述文本框 */
        [data-theme="dark"] .command-description-edit {
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(15, 23, 42, 0.98) 100%);
            border-color: rgba(100, 116, 139, 0.3);
            color: #f1f5f9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), 0 1px 3px rgba(0, 0, 0, 0.4);
        }
        
        [data-theme="dark"] .command-description-edit:focus {
            border-color: #8b5cf6;
            box-shadow: 
                0 0 0 3px rgba(139, 92, 246, 0.15),
                0 4px 16px rgba(139, 92, 246, 0.25),
                0 2px 8px rgba(0, 0, 0, 0.3);
            background: rgba(30, 41, 59, 0.98);
        }
        
        [data-theme="dark"] .command-description-edit:hover {
            border-color: rgba(139, 92, 246, 0.4);
        }
        
        [data-theme="dark"] .command-description-edit::placeholder {
            color: #64748b;
        }
        
        /* 命令描述标签样式 */
        .command-description-label {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .command-description-label::before {
            content: "💬";
            margin-right: 6px;
            font-size: 16px;
        }
        
        [data-theme="dark"] .command-description-label {
            color: #e2e8f0;
        }
        
        /* 命令描述容器 */
        .command-description-container {
            background: rgba(248, 250, 252, 0.7);
            border-radius: 12px;
            padding: 16px;
            margin: 12px 0;
            border: 1px solid rgba(99, 102, 241, 0.1);
            transition: all 0.3s ease;
        }
        
        .command-description-container:hover {
            background: rgba(248, 250, 252, 0.9);
            border-color: rgba(99, 102, 241, 0.2);
        }
        
        [data-theme="dark"] .command-description-container {
            background: rgba(30, 41, 59, 0.7);
            border-color: rgba(100, 116, 139, 0.2);
        }
        
        [data-theme="dark"] .command-description-container:hover {
            background: rgba(30, 41, 59, 0.9);
            border-color: rgba(139, 92, 246, 0.3);
        }
        
        /* 浮动动画 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        @keyframes pulse-glow {
            0%, 100% { box-shadow: 0 0 5px rgba(255, 107, 157, 0.3); }
            50% { box-shadow: 0 0 20px rgba(255, 107, 157, 0.6), 0 0 30px rgba(255, 107, 157, 0.4); }
        }
        
        /* 底部按钮区域优化 */
        .form-actions {
            margin-top: 24px;
            margin-bottom: 40px;
            padding: 20px 0;
            border-top: 1px solid rgba(99, 102, 241, 0.1);
            background: rgba(248, 250, 252, 0.5);
            border-radius: 0 0 12px 12px;
            position: sticky;
            bottom: 0;
            z-index: 10;
            backdrop-filter: blur(8px);
        }
        
        [data-theme="dark"] .form-actions {
            background: rgba(30, 41, 59, 0.8);
            border-top-color: rgba(100, 116, 139, 0.2);
        }
        
        .form-actions .btn {
            margin: 8px;
        }
        
        /* 配置区域内容区域底部间距 */
        .config-section .flex-1.overflow-y-auto {
            padding-bottom: 120px;
        }
        
        /* 主要内容区域滚动条 */
        .main-content-scroll {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
        }
        
        .main-content-scroll::-webkit-scrollbar {
            width: 8px;
        }
        
        .main-content-scroll::-webkit-scrollbar-track {
            background: rgba(243, 244, 246, 0.5);
            border-radius: 4px;
        }
        
        .main-content-scroll::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.8);
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .main-content-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(107, 114, 128, 0.9);
        }
        
        /* 侧边栏滚动条 */
        .sidebar-scroll {
            scrollbar-width: thin;
            scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
        }
        
        .sidebar-scroll::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar-scroll::-webkit-scrollbar-track {
            background: rgba(243, 244, 246, 0.3);
            border-radius: 3px;
        }
        
        .sidebar-scroll::-webkit-scrollbar-thumb {
            background: rgba(156, 163, 175, 0.7);
            border-radius: 3px;
            transition: all 0.3s ease;
        }
        
        .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(107, 114, 128, 0.8);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-purple-50 to-pink-50 font-anime" style="height: 100vh; overflow: hidden;">
    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="glass-effect rounded-2xl p-8 text-center">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-anime-pink border-t-transparent mb-4"></div>
            <p class="text-white text-lg font-medium">加载中...</p>
        </div>
    </div>

    <!-- Message Popup -->
    <div id="message-popup" class="fixed top-4 right-4 z-40 p-4 rounded-xl shadow-lg transform translate-x-full transition-transform duration-300 ease-in-out"></div>

    <!-- Header -->
    <header class="bg-gradient-to-r from-slate-800 via-slate-700 to-slate-800 shadow-lg flex-shrink-0" style="height: 80px;">
        <div class="container mx-auto px-6 py-4 h-full">
            <div class="flex items-center justify-between h-full">
                <div class="flex items-center space-x-3">
                    <!-- VCP Logo SVG -->
                    <svg class="w-10 h-10 text-white animate-float" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                    <div>
                        <h1 class="text-2xl font-bold text-white">Sentra</h1>
                        <p class="text-white text-opacity-80 text-sm">管理面板</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="restart-server-button" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0V9a8 8 0 1115.356 2M15 15v5h-.582M8.444 13a8.001 8.001 0 0015.356-2M8.444 13v0a8 8 0 00-15.356 2"/>
                        </svg>
                        <span>重启服务</span>
                    </button>
                    <button id="切换主题" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-lg transition-all duration-200">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Container - Full Screen Layout -->
    <div class="flex" style="height: calc(100vh - 80px);">
        
        <!-- Sidebar -->
        <div class="w-80 bg-white shadow-2xl flex flex-col h-full">
            <div class="p-6 border-b border-gray-200 flex-shrink-0">
                <h2 class="text-xl font-bold text-anime-dark flex items-center">
                    <svg class="w-6 h-6 mr-2 text-anime-pink" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                    配置管理
                </h2>
            </div>
            
            <div class="flex-1 overflow-y-auto sidebar-scroll">
                <nav id="plugin-nav" class="p-4">
                    <div class="space-y-1 mb-6">
                        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider px-3 py-2">核心功能</div>
                        <a href="#" data-target="base-config" class="flex items-center px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-anime-pink hover:to-anime-purple hover:text-white transition-all duration-200 group">
                            <svg class="w-5 h-5 mr-3 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            <span class="font-medium">全局配置</span>
                        </a>
                        <a href="#" data-target="daily-notes-manager" class="flex items-center px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-anime-blue hover:to-anime-green hover:text-white transition-all duration-200 group">
                            <svg class="w-5 h-5 mr-3 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                            <span class="font-medium">日记管理</span>
                        </a>
                        <a href="#" data-target="agent-files-editor" class="flex items-center px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-anime-orange hover:to-anime-red hover:text-white transition-all duration-200 group">
                            <svg class="w-5 h-5 mr-3 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            <span class="font-medium">Agent 文件</span>
                        </a>
                        <a href="#" data-target="server-log-viewer" class="flex items-center px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-anime-purple hover:to-anime-pink hover:text-white transition-all duration-200 group">
                            <svg class="w-5 h-5 mr-3 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            <span class="font-medium">服务器日志</span>
                        </a>
                        <a href="memory_nexus.html" class="flex items-center px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-indigo-500 hover:to-purple-600 hover:text-white transition-all duration-200 group">
                            <svg class="w-5 h-5 mr-3 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                            </svg>
                            <span class="font-medium">记忆深渊</span>
                        </a>
                        <a href="#" data-target="config-hot-reload" class="flex items-center px-3 py-2.5 rounded-lg text-gray-700 hover:bg-gradient-to-r hover:from-emerald-500 hover:to-teal-600 hover:text-white transition-all duration-200 group">
                            <svg class="w-5 h-5 mr-3 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                            <span class="font-medium">配置热更新</span>
                            <span class="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-emerald-100 text-emerald-800" id="hot-reload-status-badge">
                                启用
                            </span>
                        </a>
                    </div>
                    
                    <!-- 核心功能模块 -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider px-3 py-2">核心功能</div>
                        <ul class="space-y-1">
                            <!-- 世界树管理 -->
                            <li>
                                <a href="#" onclick="navigateTo('worldtree-section')"
                                   class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 transition-colors duration-200">
                                    <svg class="text-amber-500 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                    </svg>
                                    世界树管理
                                    <span class="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-amber-100 text-amber-800">
                                        角色
                                    </span>
                                </a>
                            </li>

                            <!-- 微信适配器管理 -->
                            <li>
                                <a href="#" onclick="navigateTo('wechat-adapter-section')"
                                   class="group flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-md hover:text-gray-900 hover:bg-gray-50 transition-colors duration-200">
                                    <svg class="text-green-500 mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                    微信适配器
                                    <span class="ml-auto inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800" id="wechat-status-badge">
                                        离线
                                    </span>
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 插件列表 -->
                    <div class="border-t border-gray-200 pt-4">
                        <div class="text-xs font-semibold text-gray-400 uppercase tracking-wider px-3 py-2">插件配置</div>
                        <ul class="space-y-1" id="plugin-list">
                            <!-- 插件列表将由JavaScript动态填充 -->
                        </ul>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 flex flex-col h-full relative" id="config-details-container">
                
            <!-- 世界树管理界面 -->
            <section id="worldtree-section" class="config-section flex flex-col h-full" style="display: none;">
                <!-- Header -->
                <div class="bg-gradient-to-r from-amber-600 to-orange-700 p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-8 h-8 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            </svg>
                            <div>
                                <h1 class="text-2xl font-bold">世界树管理</h1>
                                <p class="text-amber-100 mt-1">Agent角色时间架构、日程表和心理活动管理</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3">
                            <button onclick="refreshWorldTreeData()" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="bg-white border-b border-gray-200">
                    <nav class="flex space-x-8 px-6" aria-label="Tabs">
                        <button onclick="switchWorldTreeTab('agents')" class="worldtree-tab-btn py-4 px-1 border-b-2 font-medium text-sm border-amber-500 text-amber-600" data-tab="agents">
                            Agent管理
                        </button>
                        <button onclick="switchWorldTreeTab('schedules')" class="worldtree-tab-btn py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="schedules">
                            日程管理
                        </button>
                        <button onclick="switchWorldTreeTab('psychology')" class="worldtree-tab-btn py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="psychology">
                            心理活动
                        </button>
                        <button onclick="switchWorldTreeTab('config')" class="worldtree-tab-btn py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="config">
                            系统配置
                        </button>
                    </nav>
                </div>

                <!-- Content Area -->
                <div class="flex-1 overflow-hidden">
                    <!-- Agent管理标签页 -->
                    <div id="worldtree-agents-tab" class="worldtree-tab-content h-full p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
                            <!-- Agent列表 -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="p-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Agent列表</h3>
                                    <p class="text-sm text-gray-600 mt-1">选择Agent配置时间架构和背景设定</p>
                                </div>
                                <div class="p-4">
                                    <div id="worldtree-agent-list" class="space-y-2 max-h-96 overflow-y-auto">
                                        <!-- Agent列表将由JavaScript填充 -->
                                    </div>
                                </div>
                            </div>

                            <!-- Agent详情编辑 -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="p-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900">Agent配置</h3>
                                    <p class="text-sm text-gray-600 mt-1">编辑选中Agent的世界树设定</p>
                                </div>
                                <div class="p-4">
                                    <div id="worldtree-agent-editor">
                                        <div class="text-center text-gray-500 py-8">
                                            <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                            <p>请选择一个Agent进行配置</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 日程管理标签页 -->
                    <div id="worldtree-schedules-tab" class="worldtree-tab-content h-full p-6" style="display: none;">
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                            <div class="p-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">日程管理</h3>
                                        <p class="text-sm text-gray-600 mt-1">管理Agent的日程安排和时间表</p>
                                    </div>
                                    <button onclick="showAddScheduleDialog()" class="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                        添加日程
                                    </button>
                                </div>
                            </div>
                            <div class="p-4 h-full overflow-y-auto">
                                <div id="worldtree-schedules-list">
                                    <!-- 日程列表将由JavaScript填充 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 心理活动标签页 -->
                    <div id="worldtree-psychology-tab" class="worldtree-tab-content h-full p-6" style="display: none;">
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200 h-full">
                            <div class="p-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-semibold text-gray-900">心理活动</h3>
                                        <p class="text-sm text-gray-600 mt-1">查看和管理Agent的心理活动记录</p>
                                    </div>
                                    <button onclick="generatePsychologyActivity()" class="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                                        生成心理活动
                                    </button>
                                </div>
                            </div>
                            <div class="p-4 h-full overflow-y-auto">
                                <div id="worldtree-psychology-list">
                                    <!-- 心理活动列表将由JavaScript填充 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统配置标签页 -->
                    <div id="worldtree-config-tab" class="worldtree-tab-content h-full p-6" style="display: none;">
                        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                            <div class="p-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">世界树系统配置</h3>
                                <p class="text-sm text-gray-600 mt-1">配置世界树系统的参数和选项</p>
                            </div>
                            <div class="p-6">
                                <form id="worldtree-config-form" class="space-y-6">
                                    <!-- 配置表单将由JavaScript填充 -->
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 微信适配器管理界面 -->
            <section id="wechat-adapter-section" class="config-section flex flex-col h-full" style="display: none;">
                <!-- Header -->
                <div class="bg-gradient-to-r from-green-600 to-green-700 p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-8 h-8 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            <div>
                                <h1 class="text-2xl font-bold">微信适配器管理</h1>
                                <p class="text-green-100 mt-1">监控和管理微信机器人适配器</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-right">
                                <div class="text-sm text-green-100">服务状态</div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 rounded-full mr-2" id="wechat-status-indicator"></div>
                                    <span class="font-semibold" id="wechat-status-text">检查中...</span>
                                </div>
                            </div>
                            <button onclick="window.loadWeChatStatus()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-all duration-200 mr-2">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </button>
                            <button onclick="console.log('强制初始化微信适配器'); initializeWeChatAdapter();" class="bg-blue-500 bg-opacity-80 hover:bg-opacity-100 px-3 py-2 rounded-lg text-white text-sm transition-all duration-200">
                                初始化
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="flex-1 flex flex-col overflow-hidden">
                    <!-- 标签页导航 -->
                    <div class="border-b border-gray-200 bg-white flex-shrink-0">
                        <nav class="flex space-x-8 px-6" aria-label="Tabs">
                            <button onclick="switchWeChatTab('status')" class="wechat-tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200" data-tab="status">
                                状态监控
                            </button>
                            <button onclick="switchWeChatTab('config')" class="wechat-tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200" data-tab="config">
                                配置管理
                            </button>
                            <button onclick="switchWeChatTab('logs')" class="wechat-tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200" data-tab="logs">
                                日志监控
                            </button>
                            <button onclick="switchWeChatTab('listeners')" class="wechat-tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200" data-tab="listeners">
                                监听管理
                            </button>
                        </nav>
                    </div>

                    <!-- 标签页内容 -->
                    <div class="flex-1 overflow-y-auto bg-gray-50 main-content-scroll">
                        <div class="p-6">
                        <!-- 状态监控标签页 -->
                        <div id="wechat-tab-status" class="wechat-tab-content">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                                <!-- 服务状态卡片 -->
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-medium text-gray-900">服务状态</h3>
                                            <p class="text-sm text-gray-500" id="wechat-service-status">检查中...</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 连接状态卡片 -->
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-medium text-gray-900">微信连接</h3>
                                            <p class="text-sm text-gray-500" id="wechat-connection-status">检查中...</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 监听统计卡片 -->
                                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-medium text-gray-900">监听对象</h3>
                                            <p class="text-sm text-gray-500" id="wechat-listeners-count">0 个</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 微信适配器控制 -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-medium text-gray-900">微信适配器控制</h3>
                                    <p class="text-sm text-gray-500 mt-1">自动化命令控制微信适配器进程</p>
                                </div>
                                <div class="p-6">
                                    <!-- 环境管理区域 -->
                                    <div class="mb-6">
                                        <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
                                            </svg>
                                            虚拟环境管理
                                        </h4>
                                        <p class="text-sm text-gray-600 mb-4">管理Python虚拟环境，解决迁移后的路径问题</p>

                                        <!-- 环境状态显示 -->
                                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <div class="flex items-center">
                                                        <div class="w-3 h-3 rounded-full mr-2" id="env-status-indicator"></div>
                                                        <span class="font-medium text-sm" id="env-status-text">检查中...</span>
                                                    </div>
                                                    <p class="text-xs text-gray-500 mt-1" id="env-status-detail">正在检查虚拟环境状态...</p>
                                                </div>
                                                <button onclick="checkEnvironmentStatus()" class="px-3 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs transition-colors duration-200">
                                                    刷新状态
                                                </button>
                                            </div>
                                        </div>

                                        <div class="flex items-center space-x-4 flex-wrap gap-2">
                                            <button onclick="createEnvironment()" class="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                                </svg>
                                                一键配置环境
                                            </button>
                                            <button onclick="deleteEnvironment()" class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                </svg>
                                                一键删除环境
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 进程控制区域 -->
                                    <div class="mb-6 border-t pt-6">
                                        <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                            </svg>
                                            进程控制
                                        </h4>
                                        <p class="text-sm text-gray-600 mb-4">使用自动化命令 <code class="bg-gray-100 px-2 py-1 rounded text-xs">venv_py311\Scripts\python.exe wechat_api.py</code> 控制微信适配器进程</p>
                                        <div class="flex items-center space-x-4 flex-wrap gap-2">
                                            <button onclick="startWeChatBot()" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-5-4v8a2 2 0 002 2h4a2 2 0 002-2v-8"></path>
                                                </svg>
                                                启动适配器进程
                                            </button>
                                            <button onclick="stopWeChatBot()" class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path>
                                                </svg>
                                                停止适配器进程
                                            </button>
                                            <button onclick="checkWeChatProcessStatus()" class="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                                                </svg>
                                                检查状态
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 监听控制区域 -->
                                    <div class="border-t pt-6">
                                        <h4 class="text-md font-medium text-gray-900 mb-3 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"/>
                                            </svg>
                                            监听控制
                                        </h4>
                                        <p class="text-sm text-gray-600 mb-4">控制微信机器人的消息监听功能（需要适配器进程已启动）</p>
                                        <div class="flex items-center space-x-4 flex-wrap gap-2">
                                            <button onclick="startWeChatBotListening()" class="px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 14.142M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z"/>
                                                </svg>
                                                启动监听
                                            </button>
                                            <button onclick="stopWeChatBotListening()" class="px-6 py-3 bg-amber-600 text-white rounded-lg hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15zM17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2"/>
                                                </svg>
                                                停止监听
                                            </button>
                                            <button onclick="sendTestMessage()" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200 flex items-center">
                                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                                </svg>
                                                发送测试消息
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 实时监听状态 -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-medium text-gray-900">实时监听状态</h3>
                                </div>
                                <div class="p-6">
                                    <div id="wechat-listeners-status" class="space-y-4">
                                        <div class="text-center text-gray-500 py-8">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                            </svg>
                                            <p class="mt-2">正在加载监听状态...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 消息缓存监控 -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">📦 消息缓存监控</h3>
                                        <p class="text-sm text-gray-500 mt-1">实时监控智能消息缓存系统的运行状态</p>
                                        <div class="flex items-center mt-2">
                                            <div id="cache-monitor-status" class="flex items-center text-xs">
                                                <div class="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                                                <span class="text-gray-500">监控状态：等待中</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2">
                                        <button onclick="refreshCacheStats()" class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
                                            🔄 刷新
                                        </button>
                                        <button onclick="cleanupCache()" class="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 transition-colors">
                                            🧹 清理
                                        </button>
                                        <button onclick="resetCacheStats()" class="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors">
                                            🔄 重置统计
                                        </button>
                                        <button onclick="window.cacheMonitorManager && window.cacheMonitorManager.stopMonitoring()" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                                            ⏹️ 停止监控
                                        </button>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <!-- 缓存统计卡片 -->
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                                                        <span class="text-white text-sm">📥</span>
                                                    </div>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm font-medium text-blue-900">缓存消息数</p>
                                                    <p class="text-lg font-semibold text-blue-700" id="cache-total-messages">-</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                                                        <span class="text-white text-sm">📦</span>
                                                    </div>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm font-medium text-green-900">处理批次数</p>
                                                    <p class="text-lg font-semibold text-green-700" id="cache-total-batches">-</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                                                        <span class="text-white text-sm">📊</span>
                                                    </div>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm font-medium text-purple-900">平均批次大小</p>
                                                    <p class="text-lg font-semibold text-purple-700" id="cache-avg-batch-size">-</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="bg-orange-50 p-4 rounded-lg border border-orange-200">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0">
                                                    <div class="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                                                        <span class="text-white text-sm">🎯</span>
                                                    </div>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm font-medium text-orange-900">缓存命中率</p>
                                                    <p class="text-lg font-semibold text-orange-700" id="cache-hit-rate">-</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 活跃缓存列表 -->
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <h4 class="text-md font-medium text-gray-900 mb-3">🔄 活跃缓存详情</h4>
                                        <div id="active-caches-list" class="space-y-2">
                                            <!-- 动态加载活跃缓存 -->
                                            <div class="text-center text-gray-500 py-4">
                                                <p class="text-sm">暂无活跃缓存</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 配置管理标签页 -->
                        <div id="wechat-tab-config" class="wechat-tab-content hidden">
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200">
                                    <h3 class="text-lg font-medium text-gray-900">微信适配器配置</h3>
                                    <p class="text-sm text-gray-500 mt-1">管理微信适配器的运行配置</p>
                                </div>
                                <div class="p-6">
                                    <form id="wechat-config-form" class="space-y-6">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">API服务端口</label>
                                                <input type="number" name="WEB_PORT" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="7702">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">日志目录</label>
                                                <input type="text" name="LOG_DIR" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="chat_logs">
                                            </div>
                                        </div>

                                        <div>
                                            <label class="flex items-center">
                                                <input type="checkbox" name="AUTO_START_LISTENING" class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-300 focus:ring focus:ring-green-200 focus:ring-opacity-50">
                                                <span class="ml-2 text-sm text-gray-700">启动时自动开始监听</span>
                                            </label>
                                        </div>

                                        <!-- AI自动回复配置 -->
                                        <div class="border-t border-gray-200 pt-6">
                                            <div class="flex items-center justify-between mb-4">
                                                <h4 class="text-lg font-medium text-gray-900">🤖 AI自动回复配置</h4>
                                                <div class="flex space-x-2">
                                                    <button type="button" onclick="testAIReply()" class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors">
                                                        测试回复
                                                    </button>
                                                    <button type="button" onclick="testSegmentedSend()" class="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors">
                                                        测试分段
                                                    </button>
                                                    <button type="button" onclick="resetAIConfig()" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                                                        重置配置
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="space-y-6">
                                                <!-- 基础设置 -->
                                                <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-gray-400">
                                                    <h5 class="font-medium text-gray-900 mb-3">⚙️ 基础设置</h5>
                                            <div class="space-y-4">
                                                        <!-- 启用AI自动回复开关 -->
                                                        <div class="flex items-center justify-between mb-4">
                                                            <label class="text-sm font-medium text-gray-700">启用AI自动回复</label>
                                                            <div class="toggle-switch">
                                                                <input type="checkbox" name="AI_AUTO_REPLY.enabled" id="toggle-ai-enabled" class="toggle-input">
                                                                <label for="toggle-ai-enabled" class="toggle-label"></label>
                                                            </div>
                                                        </div>

                                                        <!-- 排除机器人名称 -->
                                                        <div class="mb-4">
                                                            <label class="block text-sm font-medium text-gray-700 mb-2">🚫 排除机器人名称</label>
                                                            <div id="exclude-bot-names-container" class="space-y-2">
                                                                <!-- 动态生成的输入框 -->
                                                            </div>
                                                            <button type="button" onclick="addArrayItem('exclude_bot_names')" class="mt-2 px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-sm">
                                                                + 添加排除名称
                                                            </button>
                                                            <p class="text-xs text-gray-500 mt-1">这些名称的消息将被忽略，不会触发AI回复</p>
                                                        </div>

                                                        <!-- 私聊设置 -->
                                                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                                                            <h4 class="text-lg font-semibold text-blue-800 mb-4">🔒 私聊设置</h4>

                                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">AI模型名称 <span class="text-red-500">*</span></label>
                                                                    <input type="text" name="AI_AUTO_REPLY.private_chat.model" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="gemini-2.5-pro-free">
                                                                    <p class="text-xs text-gray-500 mt-1">私聊使用的AI模型</p>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">助手名称</label>
                                                                    <input type="text" name="AI_AUTO_REPLY.private_chat.assistantName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="雨安安">
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Agent人设</label>
                                                                    <input type="text" name="AI_AUTO_REPLY.private_chat.agent" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="雨安安">
                                                                </div>
                                                            </div>

                                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">UserAgent标识符</label>
                                                                    <input type="text" name="AI_AUTO_REPLY.private_chat.useragent" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="静">
                                                                    <p class="text-xs text-gray-500 mt-1">用于AI识别用户身份</p>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Assistant ID</label>
                                                                    <input type="number" name="AI_AUTO_REPLY.private_chat.assistant_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="2857896171">
                                                                    <p class="text-xs text-gray-500 mt-1">AI助手的唯一标识ID</p>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">User ID</label>
                                                                    <input type="number" name="AI_AUTO_REPLY.private_chat.user_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="2166683295">
                                                                    <p class="text-xs text-gray-500 mt-1">用户的唯一标识ID</p>
                                                                </div>
                                                            </div>

                                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">私聊历史记录数量 <span class="text-blue-500">*</span></label>
                                                                    <input type="number" name="AI_AUTO_REPLY.private_chat.history_count" min="1" max="20" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="5">
                                                                    <p class="text-xs text-gray-500 mt-1">获取最近N条历史记录（统一处理模式）</p>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">回复概率</label>
                                                                    <input type="number" name="AI_AUTO_REPLY.private_chat.reply_probability" min="0" max="1" step="0.1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="0.8">
                                                                    <p class="text-xs text-gray-500 mt-1">私聊自动回复的概率（0.0-1.0）</p>
                                                                </div>
                                                            </div>

                                                            <div class="flex items-center justify-between mb-4">
                                                                <div>
                                                                    <label class="text-sm font-medium text-gray-700">启用图片渲染回复</label>
                                                                    <p class="text-xs text-gray-500">私聊AI回复将渲染为图片形式发送</p>
                                                                </div>
                                                                <div class="toggle-switch">
                                                                    <input type="checkbox" name="AI_AUTO_REPLY.private_chat.render_as_image" id="toggle-private-render-image" class="toggle-input">
                                                                    <label for="toggle-private-render-image" class="toggle-label"></label>
                                                                </div>
                                                            </div>

                                                            <!-- 私聊触发关键词 -->
                                                            <div class="mb-4">
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">🔑 私聊触发关键词</label>
                                                                <div id="private-trigger-keywords-container" class="space-y-2">
                                                                    <!-- 动态生成的输入框 -->
                                                                </div>
                                                                <button type="button" onclick="addArrayItem('private_trigger_keywords')" class="mt-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 text-sm">
                                                                    + 添加触发关键词
                                                                </button>
                                                                <p class="text-xs text-gray-500 mt-1">包含这些关键词的私聊消息会触发AI回复</p>
                                                            </div>

                                                            <!-- 私聊目标用户 -->
                                                            <div class="mb-4">
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">👤 私聊目标用户</label>
                                                                <div id="private-target-users-container" class="space-y-2">
                                                                    <!-- 动态生成的输入框 -->
                                                                </div>
                                                                <button type="button" onclick="addArrayItem('private_target_users')" class="mt-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 text-sm">
                                                                    + 添加目标用户
                                                                </button>
                                                                <p class="text-xs text-gray-500 mt-1">只对这些用户的私聊消息进行AI回复（留空表示对所有用户回复）</p>
                                                            </div>
                                                        </div>

                                                        <!-- 群聊设置 -->
                                                        <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                                                            <h4 class="text-lg font-semibold text-green-800 mb-4">👥 群聊设置</h4>

                                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">AI模型名称 <span class="text-red-500">*</span></label>
                                                                    <input type="text" name="AI_AUTO_REPLY.group_chat.model" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="gemini-2.5-pro-free">
                                                                    <p class="text-xs text-gray-500 mt-1">群聊使用的AI模型</p>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">助手名称</label>
                                                                    <input type="text" name="AI_AUTO_REPLY.group_chat.assistantName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="雨安安">
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Agent人设</label>
                                                                    <input type="text" name="AI_AUTO_REPLY.group_chat.agent" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="雨安安">
                                                                </div>
                                                            </div>

                                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">Assistant ID</label>
                                                                    <input type="number" name="AI_AUTO_REPLY.group_chat.assistant_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="2857896171">
                                                                    <p class="text-xs text-gray-500 mt-1">AI助手的唯一标识ID</p>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">User ID</label>
                                                                    <input type="number" name="AI_AUTO_REPLY.group_chat.user_id" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="2166683295">
                                                                    <p class="text-xs text-gray-500 mt-1">用户的唯一标识ID</p>
                                                                </div>
                                                            </div>

                                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">群聊历史记录数量 <span class="text-green-500">*</span></label>
                                                                    <input type="number" name="AI_AUTO_REPLY.group_chat.history_count" min="1" max="50" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="10">
                                                                    <p class="text-xs text-gray-500 mt-1">获取最近N条历史记录（统一处理模式）</p>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">回复概率</label>
                                                                    <input type="number" name="AI_AUTO_REPLY.group_chat.reply_probability" min="0" max="1" step="0.1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="0.3">
                                                                    <p class="text-xs text-gray-500 mt-1">群聊自动回复的概率（0.0-1.0）</p>
                                                                </div>
                                                            </div>

                                                            <div class="bg-blue-50 p-3 rounded-md border border-blue-200 mb-4">
                                                                <div class="flex items-start">
                                                                    <div class="flex-shrink-0">
                                                                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                                                        </svg>
                                                                    </div>
                                                                    <div class="ml-3">
                                                                        <p class="text-sm text-blue-800">
                                                                            <strong>统一处理模式：</strong>群聊现在使用与私聊相同的处理逻辑，UserAgent会自动使用当前发送者的名称，历史记录以role-based消息格式处理。
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="flex items-center justify-between mb-4">
                                                                <div>
                                                                    <label class="text-sm font-medium text-gray-700">启用图片渲染回复</label>
                                                                    <p class="text-xs text-gray-500">群聊AI回复将渲染为图片形式发送</p>
                                                                </div>
                                                                <div class="toggle-switch">
                                                                    <input type="checkbox" name="AI_AUTO_REPLY.group_chat.render_as_image" id="toggle-group-render-image" class="toggle-input">
                                                                    <label for="toggle-group-render-image" class="toggle-label"></label>
                                                                </div>
                                                            </div>

                                                            <!-- 群聊触发关键词 -->
                                                            <div class="mb-4">
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">🔑 群聊触发关键词</label>
                                                                <div id="group-trigger-keywords-container" class="space-y-2">
                                                                    <!-- 动态生成的输入框 -->
                                                                </div>
                                                                <button type="button" onclick="addArrayItem('group_trigger_keywords')" class="mt-2 px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm">
                                                                    + 添加触发关键词
                                                                </button>
                                                                <p class="text-xs text-gray-500 mt-1">包含这些关键词的群聊消息会触发AI回复</p>
                                                            </div>

                                                            <!-- 群聊目标群组 -->
                                                            <div class="mb-4">
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">👥 群聊目标群组</label>
                                                                <div id="group-target-users-container" class="space-y-2">
                                                                    <!-- 动态生成的输入框 -->
                                                                </div>
                                                                <button type="button" onclick="addArrayItem('group_target_users')" class="mt-2 px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm">
                                                                    + 添加目标群组
                                                                </button>
                                                                <p class="text-xs text-gray-500 mt-1">只对这些群组的消息进行AI回复（留空表示对所有群组回复）</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 触发条件 -->
                                                <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-400">
                                                    <h5 class="font-medium text-gray-900 mb-3">🎯 触发条件</h5>
                                                    <div class="space-y-4">
                                                        <!-- 关键词触发策略 -->
                                                        <div class="flex items-center justify-between">
                                                            <div>
                                                                <label class="text-sm font-medium text-gray-700">关键词必定触发</label>
                                                                <p class="text-xs text-gray-500">开启时：有关键词必定回复，无关键词按概率回复</p>
                                                            </div>
                                                            <div class="toggle-switch">
                                                                <input type="checkbox" name="AI_AUTO_REPLY.keyword_must_trigger" id="toggle-keyword-must" class="toggle-input">
                                                                <label for="toggle-keyword-must" class="toggle-label"></label>
                                                            </div>
                                                        </div>

                                                        <!-- 自动过滤机器人消息 -->
                                                        <div class="flex items-center justify-between">
                                                            <div>
                                                                <label class="text-sm font-medium text-gray-700">自动过滤机器人消息</label>
                                                                <p class="text-xs text-gray-500">自动识别并过滤机器人发送的消息，避免AI回复</p>
                                                            </div>
                                                            <div class="toggle-switch">
                                                                <input type="checkbox" name="AI_AUTO_REPLY.auto_filter_bots" id="toggle-auto-filter" class="toggle-input">
                                                                <label for="toggle-auto-filter" class="toggle-label"></label>
                                                    </div>
                                                </div>

                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-2">最小消息长度</label>
                                                    <input type="number" name="AI_AUTO_REPLY.min_message_length" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="1">
                                                    <p class="text-xs text-gray-500 mt-1">少于此长度的消息不会触发回复</p>
                                                </div>
                                            </div>
                                        </div>



                                                <!-- 高级分段发送设置 -->
                                                <div class="bg-purple-50 p-4 rounded-lg border-l-4 border-purple-400">
                                                    <h5 class="font-medium text-gray-900 mb-3">🚀 高级智能分段发送</h5>
                                                    <div class="space-y-4">
                                                        <!-- 启用分段发送开关 -->
                                                        <div class="flex items-center justify-between">
                                                            <div>
                                                                <label class="text-sm font-medium text-gray-700">启用高级智能分段发送</label>
                                                                <p class="text-xs text-gray-500">支持标点优先级分段、多媒体顺序发送等高级功能</p>
                                                            </div>
                                                            <div class="toggle-switch">
                                                                <input type="checkbox" name="AI_AUTO_REPLY.segmented_reply.enabled" id="toggle-segmented" class="toggle-input">
                                                                <label for="toggle-segmented" class="toggle-label"></label>
                                                            </div>
                                                        </div>

                                                        <!-- 基础参数配置 -->
                                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">最小触发长度</label>
                                                                <input type="number" name="AI_AUTO_REPLY.segmented_reply.min_length" min="20" max="200" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="50">
                                                                <p class="text-xs text-gray-500 mt-1">少于此长度不分段</p>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">最大分段数量</label>
                                                                <input type="number" name="AI_AUTO_REPLY.segmented_reply.max_segments" min="2" max="10" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="5">
                                                                <p class="text-xs text-gray-500 mt-1">推荐: 3-8 段</p>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">目标段落长度</label>
                                                                <input type="number" name="AI_AUTO_REPLY.segmented_reply.target_length" min="50" max="300" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="100">
                                                                <p class="text-xs text-gray-500 mt-1">理想的段落长度</p>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">最小段落长度</label>
                                                                <input type="number" name="AI_AUTO_REPLY.segmented_reply.min_segment_length" min="10" max="100" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="30">
                                                                <p class="text-xs text-gray-500 mt-1">避免过短段落</p>
                                                            </div>
                                                        </div>

                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">分段间隔 (秒)</label>
                                                                <input type="number" name="AI_AUTO_REPLY.segmented_reply.delay" step="0.1" min="0.1" max="10" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent" placeholder="1.0">
                                                                <p class="text-xs text-gray-500 mt-1">推荐: 0.5-2.0 秒，模拟真人打字节奏</p>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">标点符号处理</label>
                                                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200">
                                                                    <div>
                                                                        <span class="text-sm font-medium text-gray-700">删除分段标点</span>
                                                                        <p class="text-xs text-gray-500">分段时自动删除句末标点符号</p>
                                                                    </div>
                                                                    <div class="toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.segmented_reply.separators.remove_punctuation" id="toggle-remove-punctuation" class="toggle-input">
                                                                        <label for="toggle-remove-punctuation" class="toggle-label"></label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 分段符号优先级配置 -->
                                                        <div class="bg-white p-4 rounded-lg border border-purple-200">
                                                            <h6 class="font-medium text-gray-900 mb-3 flex items-center">
                                                                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                                </svg>
                                                                标点符号优先级配置 (从高到低)
                                                            </h6>
                                                            <div class="space-y-4">
                                                                <!-- 自定义分隔符 -->
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">🔥 自定义分隔符 (最高优先级)</label>
                                                                    <div id="custom-separators-container" class="space-y-2">
                                                                        <!-- 动态生成的输入框 -->
                                                                    </div>
                                                                    <button type="button" onclick="addSeparatorItem('custom')" class="mt-2 px-3 py-1 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 text-sm">
                                                                        + 添加自定义分隔符
                                                                    </button>
                                                                    <p class="text-xs text-gray-500 mt-1">例如：###、---、=== 等特殊符号</p>
                                                                </div>

                                                                <!-- 句末标点 -->
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">📝 句末标点 (高优先级)</label>
                                                                    <div id="sentence-endings-container" class="space-y-2">
                                                                        <!-- 动态生成的输入框 -->
                                                                    </div>
                                                                    <button type="button" onclick="addSeparatorItem('sentence_endings')" class="mt-2 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 text-sm">
                                                                        + 添加句末标点
                                                                    </button>
                                                                    <p class="text-xs text-gray-500 mt-1">句号、感叹号、问号等句末标点</p>
                                                                </div>

                                                                <!-- 分句标点 -->
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">🔗 分句标点 (中优先级)</label>
                                                                    <div id="clause-separators-container" class="space-y-2">
                                                                        <!-- 动态生成的输入框 -->
                                                                    </div>
                                                                    <button type="button" onclick="addSeparatorItem('clause_separators')" class="mt-2 px-3 py-1 bg-green-100 text-green-700 rounded-md hover:bg-green-200 text-sm">
                                                                        + 添加分句标点
                                                                    </button>
                                                                    <p class="text-xs text-gray-500 mt-1">分号、冒号等分句标点</p>
                                                                </div>

                                                                <!-- 逗号顿号 -->
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">📍 逗号顿号 (低优先级)</label>
                                                                    <div id="comma-separators-container" class="space-y-2">
                                                                        <!-- 动态生成的输入框 -->
                                                                    </div>
                                                                    <button type="button" onclick="addSeparatorItem('comma_separators')" class="mt-2 px-3 py-1 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 text-sm">
                                                                        + 添加逗号顿号
                                                                    </button>
                                                                    <p class="text-xs text-gray-500 mt-1">逗号、顿号等停顿标点</p>
                                                                </div>

                                                                <!-- 换行符 -->
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-2">📄 换行符 (最低优先级)</label>
                                                                    <div id="line-separators-container" class="space-y-2">
                                                                        <!-- 动态生成的输入框 -->
                                                                    </div>
                                                                    <button type="button" onclick="addSeparatorItem('line_separators')" class="mt-2 px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 text-sm">
                                                                        + 添加换行符
                                                                    </button>
                                                                    <p class="text-xs text-gray-500 mt-1">双换行、单换行等行分隔符</p>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 多媒体发送优化说明 -->
                                                        <div class="bg-gradient-to-r from-purple-100 to-pink-100 p-4 rounded-lg border border-purple-200">
                                                            <h6 class="font-medium text-gray-900 mb-2 flex items-center">
                                                                <svg class="w-4 h-4 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                                </svg>
                                                                🚀 多媒体发送顺序优化
                                                            </h6>
                                                            <div class="text-xs text-gray-600 space-y-1">
                                                                <p>• <strong>文本优先</strong>：所有文本内容优先发送（支持智能分段）</p>
                                                                <p>• <strong>延迟保证</strong>：延迟0.5秒确保文本先到达</p>
                                                                <p>• <strong>顺序发送</strong>：按顺序发送图片、代码文件等多媒体内容</p>
                                                                <p>• <strong>间隔控制</strong>：多媒体之间间隔0.3秒，避免发送过快</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 高级设置 -->
                                                <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-gray-400">
                                                    <h5 class="font-medium text-gray-900 mb-3">⚙️ 高级设置</h5>
                                                    <div class="space-y-4">
                                                        <!-- API配置 -->
                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">API超时时间 (秒)</label>
                                                                <input type="number" name="AI_AUTO_REPLY.api_timeout" min="5" max="120" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent" placeholder="300">
                                                                <p class="text-xs text-gray-500 mt-1">API请求超时时间</p>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">API重试次数</label>
                                                                <input type="number" name="AI_AUTO_REPLY.api_retry_count" min="1" max="10" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent" placeholder="3">
                                                                <p class="text-xs text-gray-500 mt-1">API请求失败时的重试次数</p>
                                                            </div>
                                                        </div>

                                                        <!-- 回复控制 -->
                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">回复延迟 (毫秒)</label>
                                                                <input type="number" name="AI_AUTO_REPLY.reply_delay" min="500" max="10000" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent" placeholder="2000">
                                                                <p class="text-xs text-gray-500 mt-1">模拟真人打字延迟</p>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">上下文大小</label>
                                                                <input type="number" name="AI_AUTO_REPLY.maxContextSize" min="5" max="50" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent" placeholder="20">
                                                                <p class="text-xs text-gray-500 mt-1">记忆的对话轮数</p>
                                                            </div>
                                                        </div>

                                                        <!-- 功能开关 -->
                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div class="flex items-center justify-between">
                                                                <label class="text-sm font-medium text-gray-700">启用上下文记忆</label>
                                                                <div class="toggle-switch">
                                                                    <input type="checkbox" name="AI_AUTO_REPLY.enable_context" id="toggle-context" class="toggle-input">
                                                                    <label for="toggle-context" class="toggle-label"></label>
                                                                </div>
                                                            </div>
                                                            <div class="flex items-center justify-between">
                                                                <label class="text-sm font-medium text-gray-700">启用记忆追踪</label>
                                                                <div class="toggle-switch">
                                                                    <input type="checkbox" name="AI_AUTO_REPLY.memory_tracking" id="toggle-memory" class="toggle-input">
                                                                    <label for="toggle-memory" class="toggle-label"></label>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="flex items-center justify-between">
                                                            <div>
                                                                <label class="text-sm font-medium text-gray-700">启用调试模式</label>
                                                                <p class="text-xs text-gray-500">在控制台显示详细AI回复日志</p>
                                                            </div>
                                                            <div class="toggle-switch">
                                                                <input type="checkbox" name="AI_AUTO_REPLY.debug" id="toggle-debug" class="toggle-input">
                                                                <label for="toggle-debug" class="toggle-label"></label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 消息缓存配置 -->
                                                <div class="bg-orange-50 p-4 rounded-lg border-l-4 border-orange-400">
                                                    <h5 class="font-medium text-gray-900 mb-3">📦 智能消息缓存</h5>
                                                    <div class="space-y-4">
                                                        <!-- 缓存开关 -->
                                                        <div class="flex items-center justify-between">
                                                            <div>
                                                                <label class="text-sm font-medium text-gray-700">启用消息缓存</label>
                                                                <p class="text-xs text-gray-500">智能合并连续消息，避免逐条回复</p>
                                                            </div>
                                                            <div class="toggle-switch">
                                                                <input type="checkbox" name="AI_AUTO_REPLY.message_cache.enabled" id="toggle-message-cache" class="toggle-input">
                                                                <label for="toggle-message-cache" class="toggle-label"></label>
                                                            </div>
                                                        </div>

                                                        <!-- 缓存参数 -->
                                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">等待时间 (秒)</label>
                                                                <input type="number" name="AI_AUTO_REPLY.message_cache.wait_time" min="1" max="30" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="5">
                                                                <p class="text-xs text-gray-500 mt-1">收到消息后等待更多消息的时间</p>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-2">最大等待周期</label>
                                                                <input type="number" name="AI_AUTO_REPLY.message_cache.max_wait_cycles" min="1" max="10" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent" placeholder="3">
                                                                <p class="text-xs text-gray-500 mt-1">最多等待几个周期后强制处理</p>
                                                            </div>
                                                        </div>

                                                        <!-- 缓存调试 -->
                                                        <div class="flex items-center justify-between">
                                                            <div>
                                                                <label class="text-sm font-medium text-gray-700">缓存调试模式</label>
                                                                <p class="text-xs text-gray-500">显示详细的消息缓存处理日志</p>
                                                            </div>
                                                            <div class="toggle-switch">
                                                                <input type="checkbox" name="AI_AUTO_REPLY.message_cache.debug" id="toggle-cache-debug" class="toggle-input">
                                                                <label for="toggle-cache-debug" class="toggle-label"></label>
                                                            </div>
                                                        </div>

                                                        <!-- 缓存说明 -->
                                                        <div class="bg-orange-100 p-3 rounded-md border border-orange-200">
                                                            <div class="flex items-start">
                                                                <div class="flex-shrink-0">
                                                                    <svg class="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor">
                                                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                                                    </svg>
                                                                </div>
                                                                <div class="ml-3">
                                                                    <p class="text-sm text-orange-800">
                                                                        <strong>工作原理：</strong>根据用户名、机器人名和聊天类型生成唯一ID，将连续消息缓存合并后统一处理，避免频繁的AI调用和回复。支持多线程并发处理不同用户的消息。
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 科学算法配置 -->
                                                <div class="bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50 p-5 rounded-xl border-2 border-purple-200 shadow-sm">
                                                    <div class="flex items-center justify-between mb-4">
                                                        <div class="flex items-center space-x-2">
                                                            <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-lg flex items-center justify-center">
                                                                <span class="text-white text-sm">🧠</span>
                                                            </div>
                                                            <div>
                                                                <h5 class="font-semibold text-gray-900">科学算法配置</h5>
                                                                <p class="text-xs text-gray-500">基于真实心理学理论的AI状态分析</p>
                                                            </div>
                                                        </div>
                                                        <div class="toggle-switch">
                                                            <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.enabled" id="toggle-scientific" class="toggle-input">
                                                            <label for="toggle-scientific" class="toggle-label"></label>
                                                        </div>
                                                    </div>

                                                    <!-- 科学算法详细配置 -->
                                                    <div id="scientific-algorithms-details" class="space-y-4">
                                                        <!-- 情绪分析算法 -->
                                                        <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl border border-purple-100 shadow-sm hover:shadow-md transition-shadow">
                                                            <div class="flex items-center space-x-2 mb-3">
                                                                <div class="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center">
                                                                    <span class="text-purple-600 text-xs">💭</span>
                                                                </div>
                                                                <h6 class="text-sm font-semibold text-purple-700">情绪分析算法</h6>
                                                            </div>
                                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                                <div class="flex items-center justify-between p-2 bg-purple-25 rounded-lg hover:bg-purple-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">Russell环状模型</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.emotion_analysis.russell_model" id="russell-toggle" class="mini-toggle-input">
                                                                        <label for="russell-toggle" class="mini-toggle-label bg-purple-200"></label>
                                                                    </div>
                                                                </div>
                                                                <div class="flex items-center justify-between p-2 bg-purple-25 rounded-lg hover:bg-purple-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">PAD情感空间</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.emotion_analysis.pad_model" id="pad-toggle" class="mini-toggle-input">
                                                                        <label for="pad-toggle" class="mini-toggle-label bg-purple-200"></label>
                                                                    </div>
                                                                </div>
                                                                <div class="flex items-center justify-between p-2 bg-purple-25 rounded-lg hover:bg-purple-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">Plutchik情感轮盘</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.emotion_analysis.plutchik_wheel" id="plutchik-toggle" class="mini-toggle-input">
                                                                        <label for="plutchik-toggle" class="mini-toggle-label bg-purple-200"></label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 压力分析算法 -->
                                                        <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl border border-blue-100 shadow-sm hover:shadow-md transition-shadow">
                                                            <div class="flex items-center space-x-2 mb-3">
                                                                <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                                                                    <span class="text-blue-600 text-xs">⚡</span>
                                                                </div>
                                                                <h6 class="text-sm font-semibold text-blue-700">压力分析算法</h6>
                                                            </div>
                                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                                <div class="flex items-center justify-between p-2 bg-blue-25 rounded-lg hover:bg-blue-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">Yerkes-Dodson定律</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.stress_analysis.yerkes_dodson" id="yerkes-toggle" class="mini-toggle-input">
                                                                        <label for="yerkes-toggle" class="mini-toggle-label bg-blue-200"></label>
                                                                    </div>
                                                                </div>
                                                                <div class="flex items-center justify-between p-2 bg-blue-25 rounded-lg hover:bg-blue-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">一般适应综合症</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.stress_analysis.gas_theory" id="gas-toggle" class="mini-toggle-input">
                                                                        <label for="gas-toggle" class="mini-toggle-label bg-blue-200"></label>
                                                                    </div>
                                                                </div>
                                                                <div class="flex items-center justify-between p-2 bg-blue-25 rounded-lg hover:bg-blue-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">Lazarus认知评价</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.stress_analysis.lazarus_appraisal" id="lazarus-toggle" class="mini-toggle-input">
                                                                        <label for="lazarus-toggle" class="mini-toggle-label bg-blue-200"></label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 关系分析算法 -->
                                                        <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl border border-green-100 shadow-sm hover:shadow-md transition-shadow">
                                                            <div class="flex items-center space-x-2 mb-3">
                                                                <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center">
                                                                    <span class="text-green-600 text-xs">💕</span>
                                                                </div>
                                                                <h6 class="text-sm font-semibold text-green-700">关系分析算法</h6>
                                                            </div>
                                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                                <div class="flex items-center justify-between p-2 bg-green-25 rounded-lg hover:bg-green-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">Sternberg三元理论</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.relationship_analysis.sternberg_theory" id="sternberg-toggle" class="mini-toggle-input">
                                                                        <label for="sternberg-toggle" class="mini-toggle-label bg-green-200"></label>
                                                                    </div>
                                                                </div>
                                                                <div class="flex items-center justify-between p-2 bg-green-25 rounded-lg hover:bg-green-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">Levinger发展阶段</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.relationship_analysis.levinger_stages" id="levinger-toggle" class="mini-toggle-input">
                                                                        <label for="levinger-toggle" class="mini-toggle-label bg-green-200"></label>
                                                                    </div>
                                                                </div>
                                                                <div class="flex items-center justify-between p-2 bg-green-25 rounded-lg hover:bg-green-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">社交渗透理论</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.relationship_analysis.social_penetration" id="social-toggle" class="mini-toggle-input">
                                                                        <label for="social-toggle" class="mini-toggle-label bg-green-200"></label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 认知分析算法 -->
                                                        <div class="bg-white/80 backdrop-blur-sm p-4 rounded-xl border border-orange-100 shadow-sm hover:shadow-md transition-shadow">
                                                            <div class="flex items-center space-x-2 mb-3">
                                                                <div class="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center">
                                                                    <span class="text-orange-600 text-xs">🧮</span>
                                                                </div>
                                                                <h6 class="text-sm font-semibold text-orange-700">认知分析算法</h6>
                                                            </div>
                                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                                <div class="flex items-center justify-between p-2 bg-orange-25 rounded-lg hover:bg-orange-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">Dawkins模因理论</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.cognition_analysis.dawkins_memes" id="dawkins-toggle" class="mini-toggle-input">
                                                                        <label for="dawkins-toggle" class="mini-toggle-label bg-orange-200"></label>
                                                                    </div>
                                                                </div>
                                                                <div class="flex items-center justify-between p-2 bg-orange-25 rounded-lg hover:bg-orange-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">认知负荷理论</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.cognition_analysis.cognitive_load" id="cognitive-toggle" class="mini-toggle-input">
                                                                        <label for="cognitive-toggle" class="mini-toggle-label bg-orange-200"></label>
                                                                    </div>
                                                                </div>
                                                                <div class="flex items-center justify-between p-2 bg-orange-25 rounded-lg hover:bg-orange-50 transition-colors">
                                                                    <span class="text-xs font-medium text-gray-700">Bandura社会认知</span>
                                                                    <div class="mini-toggle-switch">
                                                                        <input type="checkbox" name="AI_AUTO_REPLY.scientific_algorithms.cognition_analysis.bandura_theory" id="bandura-toggle" class="mini-toggle-input">
                                                                        <label for="bandura-toggle" class="mini-toggle-label bg-orange-200"></label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="mt-4 p-3 bg-gradient-to-r from-yellow-50 to-amber-50 border border-yellow-200 rounded-lg">
                                                        <div class="flex items-start space-x-2">
                                                            <div class="w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center mt-0.5">
                                                                <span class="text-white text-xs">ℹ</span>
                                                            </div>
                                                            <div>
                                                                <p class="text-xs text-yellow-800 font-medium">算法说明</p>
                                                                <p class="text-xs text-yellow-700 mt-1">
                                                                    科学算法配置将影响AI状态分析的深度和精度。启用更多算法将提供更丰富的心理学分析，但可能增加计算负荷。
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="flex justify-end space-x-4">
                                            <button type="button" onclick="loadWeChatConfig()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                                重新加载
                                            </button>
                                            <button type="button" onclick="saveWeChatConfig()" class="px-4 py-2 bg-green-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                                保存配置
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- 日志监控标签页 -->
                        <div id="wechat-tab-logs" class="wechat-tab-content hidden">
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">聊天日志</h3>
                                        <p class="text-sm text-gray-500 mt-1">查看和管理微信聊天记录</p>
                                    </div>
                                    <div class="flex space-x-2">
                                        <select id="wechat-log-user-select" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-green-500">
                                            <option value="">选择用户...</option>
                                        </select>
                                        <button onclick="loadWeChatLogs()" class="px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                            加载日志
                                        </button>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <div id="wechat-logs-content" class="bg-gray-50 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
                                        <div class="text-center text-gray-500 py-8">
                                            选择用户并点击"加载日志"查看聊天记录
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 监听管理标签页 -->
                        <div id="wechat-tab-listeners" class="wechat-tab-content hidden">
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900">监听对象管理</h3>
                                        <p class="text-sm text-gray-500 mt-1">添加、删除和管理微信监听对象</p>
                                    </div>
                                    <button onclick="showAddListenerDialog()" class="px-4 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                        添加监听对象
                                    </button>
                                </div>
                                <div class="p-6">
                                    <div id="wechat-tab-listeners" class="space-y-4">
                                        <div class="text-center text-gray-500 py-8">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                            </svg>
                                            <p class="mt-2">正在加载监听对象列表...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 配置热更新管理界面 -->
            <section id="config-hot-reload-section" class="config-section flex flex-col h-full" style="display: none;">
                <!-- Header -->
                <div class="bg-gradient-to-r from-emerald-600 to-teal-700 p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-8 h-8 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                            </svg>
                            <div>
                                <h1 class="text-2xl font-bold">配置热更新管理</h1>
                                <p class="text-emerald-100 mt-1">实时监控和管理配置文件变更，无需重启服务器</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-right">
                                <div class="text-sm text-emerald-200">系统状态</div>
                                <div class="text-lg font-semibold" id="hot-reload-system-status">运行中</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 内容区域 -->
                <div class="flex-1 flex flex-col overflow-hidden">
                    <!-- 标签页导航 -->
                    <div class="border-b border-gray-200 bg-white flex-shrink-0">
                        <nav class="flex space-x-8 px-6" aria-label="Tabs">
                            <button onclick="switchHotReloadTab('status')" class="hot-reload-tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200" data-tab="status">
                                系统状态
                            </button>
                            <button onclick="switchHotReloadTab('configs')" class="hot-reload-tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200" data-tab="configs">
                                配置监控
                            </button>
                            <button onclick="switchHotReloadTab('logs')" class="hot-reload-tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200" data-tab="logs">
                                更新日志
                            </button>
                        </nav>
                    </div>

                    <!-- 标签页内容 -->
                    <div class="flex-1 overflow-y-auto bg-gray-50 main-content-scroll">
                        <div class="p-6">
                            <!-- 系统状态标签页 -->
                            <div id="hot-reload-status-tab" class="hot-reload-tab-content">
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                                    <!-- 系统状态卡片 -->
                                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <h3 class="text-sm font-medium text-gray-900">系统状态</h3>
                                                <p class="text-2xl font-semibold text-gray-900" id="hot-reload-status-text">运行中</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 监控文件数量 -->
                                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <h3 class="text-sm font-medium text-gray-900">监控文件</h3>
                                                <p class="text-2xl font-semibold text-gray-900" id="hot-reload-watched-count">0</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 注册回调数量 -->
                                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="ml-4">
                                                <h3 class="text-sm font-medium text-gray-900">活跃回调</h3>
                                                <p class="text-2xl font-semibold text-gray-900" id="hot-reload-callbacks-count">0</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">系统操作</h3>
                                    <div class="flex flex-wrap gap-4">
                                        <button onclick="refreshHotReloadStatus()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors duration-200">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                            </svg>
                                            刷新状态
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 配置监控标签页 -->
                            <div id="hot-reload-configs-tab" class="hot-reload-tab-content" style="display: none;">
                                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <h3 class="text-lg font-medium text-gray-900">配置文件监控</h3>
                                        <p class="text-sm text-gray-500 mt-1">当前正在监控的配置文件列表</p>
                                    </div>
                                    <div class="p-6">
                                        <div id="hot-reload-configs-list" class="space-y-4">
                                            <!-- 配置列表将在这里动态加载 -->
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 更新日志标签页 -->
                            <div id="hot-reload-logs-tab" class="hot-reload-tab-content" style="display: none;">
                                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                                    <div class="px-6 py-4 border-b border-gray-200">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <h3 class="text-lg font-medium text-gray-900">配置更新日志</h3>
                                                <p class="text-sm text-gray-500 mt-1">最近的配置文件更新记录</p>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                <!-- 筛选器 -->
                                                <select id="log-filter-select" class="text-sm border border-gray-300 rounded-lg px-3 py-2 bg-white focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500" onchange="filterLogs()">
                                                    <option value="all">全部日志</option>
                                                    <option value="file_change">文件变更</option>
                                                    <option value="manual_reload">手动重载</option>
                                                    <option value="success">仅成功</option>
                                                    <option value="error">仅错误</option>
                                                </select>

                                                <!-- 刷新按钮 -->
                                                <button onclick="loadHotReloadLogs()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors duration-200">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                                                    </svg>
                                                    刷新
                                                </button>

                                                <!-- 清空日志按钮 -->
                                                <button onclick="clearHotReloadLogs()" class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                                    </svg>
                                                    清空日志
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 统计信息 -->
                                    <div id="logs-stats" class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                                        <div class="flex items-center justify-between text-sm">
                                            <div class="flex items-center space-x-6">
                                                <span class="text-gray-600">总计: <span id="total-logs-count" class="font-medium text-gray-900">0</span></span>
                                                <span class="text-gray-600">成功: <span id="success-logs-count" class="font-medium text-green-600">0</span></span>
                                                <span class="text-gray-600">失败: <span id="error-logs-count" class="font-medium text-red-600">0</span></span>
                                            </div>
                                            <div class="text-gray-500">
                                                最后更新: <span id="last-update-time">-</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="p-6">
                                        <div id="hot-reload-logs-list" class="space-y-4">
                                            <!-- 日志列表将在这里动态加载 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Base Config Section -->
            <section id="base-config-section" class="config-section flex flex-col h-full" style="display: none;">
                <!-- Header -->
                <div class="bg-gradient-to-r from-slate-600 to-slate-700 p-6 text-white">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <div>
                            <h2 class="text-2xl font-bold">全局配置</h2>
                            <p class="text-white text-opacity-80">管理 Sentra 的全局设置</p>
                        </div>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="flex-1 bg-gray-50 flex flex-col">
                    <div class="flex-1 overflow-y-auto main-content-scroll">
                        <div class="p-6">
                            <form id="base-config-form" class="space-y-6 max-w-none">
                                <!-- 配置项将由JavaScript动态生成 -->
                            </form>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Daily Notes Manager Section -->
            <section id="daily-notes-manager-section" class="config-section flex flex-col h-full" style="display: none;">
                <!-- Header -->
                <div class="bg-gradient-to-r from-blue-600 to-teal-600 p-6 text-white">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                        <div>
                            <h2 class="text-2xl font-bold">日记管理器</h2>
                            <p class="text-white text-opacity-80">管理和编辑每日笔记</p>
                        </div>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="flex-1 overflow-y-auto bg-gray-50 main-content-scroll">
                    <div class="p-6">
                        <div class="flex h-full gap-6">
                            <!-- 文件夹侧边栏 -->
                            <div class="w-80 bg-white rounded-xl shadow-sm p-4 flex-shrink-0">
                                <h3 class="font-semibold text-anime-dark mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-anime-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"/>
                                    </svg>
                                    文件夹
                                </h3>
                                <div class="overflow-y-auto max-h-96 main-content-scroll">
                                    <ul id="notes-folder-list" class="space-y-2">
                                        <!-- 文件夹列表将由JavaScript动态填充 -->
                                    </ul>
                                </div>
                            </div>

                            <!-- 主内容区域 -->
                            <div class="flex-1 flex flex-col">
                                <!-- 工具栏 -->
                                <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
                                    <div class="flex flex-wrap items-center gap-4">
                                        <input type="text" id="search-daily-notes" placeholder="搜索日记..." class="flex-1 min-w-0 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-pink focus:border-transparent">
                                        <button id="move-selected-notes" disabled class="bg-anime-blue hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                            移动选中
                                        </button>
                                        <select id="move-target-folder" disabled class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-pink focus:border-transparent">
                                            <option value="">选择目标文件夹...</option>
                                        </select>
                                        <button id="delete-selected-notes-button" disabled class="bg-anime-red hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                            删除选中
                                        </button>
                                    </div>
                                    <p id="notes-action-status" class="text-sm text-gray-600 mt-2"></p>
                                </div>

                                <!-- 日记列表视图 -->
                                <div class="flex-1 overflow-y-auto main-content-scroll">
                                    <div id="notes-list-view" class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 diary-grid">
                                        <!-- 日记卡片将由JavaScript动态生成 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 日记编辑器 -->
                        <div id="note-editor-area" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4" style="display: none;">
                            <div class="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-5/6 flex flex-col">
                                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                                    <h3 class="text-xl font-bold text-anime-dark">编辑日记</h3>
                                    <div class="flex items-center space-x-4">
                                        <span id="note-editor-status" class="text-sm text-gray-600"></span>
                                        <button id="save-note-content" class="bg-anime-green hover:bg-green-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                            保存
                                        </button>
                                        <button id="cancel-edit-note" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                                            取消
                                        </button>
                                    </div>
                                </div>
                                <div class="flex-1 p-6">
                        <input type="hidden" id="editing-note-folder">
                        <input type="hidden" id="editing-note-file">
                                    <textarea id="note-content-editor" class="w-full h-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-pink focus:border-transparent font-mono resize-none main-content-scroll" placeholder="在这里编辑日记内容..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Agent Files Editor Section -->
            <section id="agent-files-editor-section" class="config-section flex flex-col h-full" style="display: none;">
                <!-- Header -->
                <div class="bg-gradient-to-r from-orange-600 to-red-600 p-6 text-white">
                    <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        <div>
                            <h2 class="text-2xl font-bold">Agent 文件编辑器</h2>
                            <p class="text-white text-opacity-80">编辑 Agent 角色配置文件</p>
                        </div>
                        </div>
                        <button id="create-agent-button" class="bg-white text-orange-600 hover:bg-orange-50 px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                            </svg>
                            新建 Agent
                        </button>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="flex-1 overflow-y-auto bg-gray-50 main-content-scroll">
                    <div class="p-6">
                        <!-- Agent信息卡片 -->
                        <div class="bg-white rounded-xl shadow-sm p-6 mb-6 agent-card">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-anime-dark flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-anime-orange" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    Agent 文件列表
                                </h3>
                                <button id="refresh-agent-list" class="bg-anime-blue hover:bg-blue-600 text-white px-3 py-1 rounded-lg text-sm transition-colors duration-200">
                                    刷新
                                </button>
                            </div>
                            <div id="agent-files-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6 agent-grid">
                                <!-- Agent文件卡片将由JavaScript动态生成 -->
                            </div>
                        </div>

                    <div class="space-y-6">
                            <div class="bg-white rounded-xl shadow-sm p-6 agent-card">
                            <label for="agent-file-select" class="block text-sm font-medium text-gray-700 mb-2">选择 Agent 文件</label>
                                <select id="agent-file-select" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-pink focus:border-transparent modern-input">
                        <option value="">请选择一个文件...</option>
                    </select>
                        </div>

                            <div class="bg-white rounded-xl shadow-sm p-6 agent-card">
                            <div class="flex items-center justify-between mb-4">
                                <label for="agent-file-content-editor" class="block text-sm font-medium text-gray-700">文件内容</label>
                                <div class="flex items-center space-x-4">
                                    <span id="agent-file-status" class="text-sm text-gray-600"></span>
                                    <button id="save-agent-file-button" disabled class="bg-anime-green hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                        保存文件
                                    </button>
                                </div>
                            </div>
                                <textarea id="agent-file-content-editor" rows="20" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-pink focus:border-transparent font-mono main-content-scroll modern-input" placeholder="选择一个 Agent 文件以编辑其内容..."></textarea>
                        </div>
                    </div>
                    </div>
                </div>
                <!-- 新建Agent对话框 -->
                <div id="create-agent-dialog" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
                    <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-lg mx-4">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-semibold text-gray-900">新建 Agent</h3>
                            <button id="close-create-dialog" class="text-gray-400 hover:text-gray-500">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                            </button>
                        </div>
                        <div class="space-y-4">
                            <div>
                                <label for="new-agent-name" class="block text-sm font-medium text-gray-700 mb-1">Agent 名称</label>
                                <input type="text" id="new-agent-name" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-pink focus:border-transparent" placeholder="输入Agent名称...">
                            </div>
                            <div>
                                <label for="new-agent-content" class="block text-sm font-medium text-gray-700 mb-1">初始内容</label>
                                <textarea id="new-agent-content" rows="10" class="w-full p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-anime-pink focus:border-transparent font-mono" placeholder="输入Agent的初始内容..."></textarea>
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end space-x-3">
                            <button id="cancel-create-agent" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                取消
                            </button>
                            <button id="confirm-create-agent" class="px-4 py-2 bg-anime-orange hover:bg-orange-600 text-white rounded-lg transition-colors duration-200">
                                创建
                            </button>
                    </div>
                    </div>
                </div>
            </section>

            <!-- Server Log Viewer Section -->
            <section id="server-log-viewer-section" class="config-section flex flex-col h-full" style="display: none;">
                <!-- Header -->
                <div class="bg-gradient-to-r from-purple-600 to-pink-600 p-6 text-white">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg class="w-8 h-8 mr-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            <div>
                                <h2 class="text-2xl font-bold">服务器日志查看器</h2>
                                <p class="text-white text-opacity-80">查看实时服务器日志</p>
                            </div>
                        </div>
                        <button id="copy-server-log-button" class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"/>
                            </svg>
                            <span>复制日志</span>
                        </button>
                    </div>
                </div>
                
                <!-- Content -->
                <div class="flex-1 overflow-y-auto bg-gray-50 main-content-scroll">
                    <div class="p-6 h-full flex flex-col">
                        <!-- 状态信息区 -->
                        <div class="bg-white rounded-xl shadow-sm p-6 mb-6">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-lg font-semibold text-anime-dark mb-2 flex items-center">
                                        <svg class="w-5 h-5 mr-2 text-anime-purple" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                        日志状态
                                    </h4>
                                    <p id="server-log-path-display" class="text-sm text-gray-600 mb-1"></p>
                                    <p id="server-log-status" class="text-sm font-medium"></p>
                                </div>
                                <div class="flex items-center space-x-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                    <span class="text-sm text-gray-600">实时更新中</span>
                                </div>
                                    <button id="refresh-logs-button" class="bg-anime-purple hover:bg-purple-600 text-white px-3 py-1 rounded-lg text-sm transition-colors duration-200">
                                        刷新日志
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 日志控制区 -->
                        <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
                            <div class="flex items-center space-x-4">
                                <label class="text-sm font-medium text-gray-700">显示行数:</label>
                                <select id="log-lines-select" class="px-3 py-1 border border-gray-300 rounded-lg text-sm">
                                    <option value="50">50行</option>
                                    <option value="100" selected>100行</option>
                                    <option value="200">200行</option>
                                    <option value="500">500行</option>
                                </select>
                                <label class="text-sm font-medium text-gray-700">过滤级别:</label>
                                <select id="log-level-filter" class="px-3 py-1 border border-gray-300 rounded-lg text-sm">
                                    <option value="">全部</option>
                                    <option value="error">错误</option>
                                    <option value="warning">警告</option>
                                    <option value="info">信息</option>
                                    <option value="success">成功</option>
                                </select>
                                <button id="clear-log-display" class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-lg text-sm transition-colors duration-200">
                                    清空显示
                                </button>
                            </div>
                        </div>
                        
                        <!-- 日志内容区 -->
                        <div class="flex-1 bg-gray-900 rounded-xl shadow-lg overflow-hidden log-viewer">
                            <div class="bg-gray-800 px-6 py-3 border-b border-gray-700">
                                <div class="flex items-center justify-between">
                                    <h4 class="text-white font-medium flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3"/>
                                        </svg>
                                        服务器日志输出
                                    </h4>
                                    <div class="flex items-center space-x-2 text-xs text-gray-400">
                                        <span>自动刷新</span>
                                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-1 overflow-y-auto p-4 main-content-scroll">
                                <pre id="server-log-content" class="text-green-400 text-sm font-mono whitespace-pre-wrap leading-relaxed">
                                    <div class="flex items-center space-x-2 text-gray-500">
                                        <div class="loading-spinner"></div>
                                        <span>正在加载日志...</span>
                                    </div>
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

                <!-- 动态插件配置区域将由JavaScript生成 -->

            </div>
    </div>
    </div>

    <!-- Live2D 模型容器 -->
    <div id="live2d-widget" class="fixed bottom-0 right-0 z-30" style="width: 280px; height: 250px; pointer-events: none;">
        <!-- Live2D控制面板和内容将由新的Live2DManager动态创建 -->
    </div>

    <!-- Live2D 模型选择器 -->
    <div id="live2d-model-selector" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center" style="display: none;">
        <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-3/4 overflow-hidden">
            <div class="bg-gradient-to-r from-anime-pink to-anime-purple p-6 text-white">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-bold">选择Live2D模型</h3>
                    <button id="close-model-selector" class="text-white hover:bg-white hover:bg-opacity-20 p-2 rounded-lg transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="p-6 overflow-y-auto max-h-96">
                <div id="model-grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                    <!-- 模型选项将由JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 引入jQuery (Live2D可能需要) -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- 引入Live2D库 -->
    <script src="/live2d/live2d.min.js"></script>
    
    <!-- Live2D Widget CDN -->
    <script src="https://fastly.jsdelivr.net/npm/live2d-widget@3.x/lib/L2Dwidget.min.js"></script>
    
    <!-- 备用Live2D加载器 -->
    <script>
        console.log('开始检查Live2D库状态...');
        
        // 检查L2Dwidget
        if (typeof L2Dwidget !== 'undefined') {
            console.log('✅ L2Dwidget已从CDN加载');
            } else {
            console.log('❌ L2Dwidget未加载，将在script.js中尝试动态加载');
        }
        
        // 检查传统live2d
        if (typeof loadlive2d !== 'undefined') {
            console.log('✅ loadlive2d函数已可用');
        } else {
            console.log('❌ loadlive2d函数未可用');
            // 尝试加载经典live2d
            const script = document.createElement('script');
            script.src = 'https://fastly.jsdelivr.net/gh/fghrsh/live2d_demo@master/assets/autoload.js';
            script.onload = () => console.log('✅ 经典live2d从CDN加载成功');
            script.onerror = () => console.log('❌ 经典live2d CDN加载失败');
            document.head.appendChild(script);
        }
        
        // 检查基础Live2D库
        if (typeof Live2D !== 'undefined') {
            console.log('✅ 基础Live2D库已加载');
        } else {
            console.log('❌ 基础Live2D库未加载');
        }
    </script>
    <script src="script.js"></script>
</body>
</html>