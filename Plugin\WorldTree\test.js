// Plugin/WorldTree/test.js - 世界树插件测试脚本
const path = require('path');
const WorldTreePlugin = require('./PluginInterface.js');

async function testWorldTreePlugin() {
    console.log('🌳 开始测试世界树插件...\n');
    
    try {
        // 创建插件实例
        const plugin = new WorldTreePlugin();
        
        // 测试配置
        const config = {
            DebugMode: true,
            AI_API_URL: 'https://api.openai.com/v1',
            AI_API_KEY: process.env.OPENAI_API_KEY || 'test_key',
            AI_MODEL: 'gpt-4o-mini',
            PSYCHOLOGY_GENERATION_ENABLED: false, // 测试时禁用AI调用
            MAX_PSYCHOLOGY_LENGTH: 500,
            SCHEDULE_UPDATE_INTERVAL: 60
        };
        
        console.log('1. 初始化插件...');
        const initResult = await plugin.initialize(config);
        console.log('初始化结果:', initResult);
        
        if (!initResult.success) {
            throw new Error(`初始化失败: ${initResult.error}`);
        }
        
        console.log('\n2. 创建测试角色...');
        const createResult = await plugin.execute({
            action: 'create_character',
            character_name: '测试角色',
            time_structure: '现代都市设定，2024年12月',
            description: '用于测试的虚拟角色'
        });
        console.log('创建角色结果:', createResult);
        
        console.log('\n3. 获取角色列表...');
        const listResult = await plugin.execute({
            action: 'get_characters'
        });
        console.log('角色列表:', listResult);
        
        console.log('\n4. 添加日程...');
        const scheduleResult = await plugin.execute({
            action: 'add_schedule',
            character_name: '测试角色',
            schedule_type: 'daily',
            title: '测试日程',
            description: '这是一个测试日程',
            start_time: new Date().toISOString(),
            priority: 1
        });
        console.log('添加日程结果:', scheduleResult);
        
        console.log('\n5. 获取角色上下文...');
        const contextResult = await plugin.execute({
            action: 'get_character_context',
            character_name: '测试角色',
            include_schedule: true,
            include_psychology: true
        });
        console.log('角色上下文:', JSON.stringify(contextResult, null, 2));
        
        console.log('\n6. 获取系统消息...');
        const systemMessageResult = await plugin.execute({
            action: 'get_system_message',
            character_name: '测试角色'
        });
        console.log('系统消息结果:', systemMessageResult);
        
        console.log('\n7. 更新角色状态...');
        const stateResult = await plugin.execute({
            action: 'update_character_state',
            character_name: '测试角色',
            current_mood: '开心',
            energy_level: 0.8,
            stress_level: 0.2,
            active_goals: ['完成测试', '学习新技能']
        });
        console.log('更新状态结果:', stateResult);
        
        console.log('\n8. 再次获取系统消息（测试缓存）...');
        const systemMessageResult2 = await plugin.execute({
            action: 'get_system_message',
            character_name: '测试角色'
        });
        console.log('系统消息结果2:', systemMessageResult2);
        
        console.log('\n9. 测试不存在的角色...');
        const nonExistentResult = await plugin.execute({
            action: 'get_system_message',
            character_name: '不存在的角色'
        });
        console.log('不存在角色结果:', nonExistentResult);
        
        // 清理资源
        await plugin.cleanup();
        
        console.log('\n✅ 世界树插件测试完成！');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        console.error('错误堆栈:', error.stack);
        process.exit(1);
    }
}

// 运行测试
if (require.main === module) {
    testWorldTreePlugin();
}
