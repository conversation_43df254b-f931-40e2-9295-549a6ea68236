// Plugin/WorldTree/PluginInterface.js - 世界树VCP插件接口
const path = require('path');
const fs = require('fs').promises;
const WorldTreeSystem = require('./WorldTree');
const logger = require('../../utils/logger.cjs');

class WorldTreePlugin {
    constructor() {
        this.name = 'WorldTree';
        this.displayName = '世界树角色管理系统';
        this.version = '1.0.0';
        this.pluginDir = __dirname;
        this.config = {};
        this.worldTree = null;
        this.isInitialized = false;
        
        // 缓存系统消息，避免频繁生成
        this.systemMessageCache = new Map();
        this.cacheExpiry = 5 * 60 * 1000; // 5分钟缓存
    }

    /**
     * 初始化插件
     */
    async initialize(config = {}) {
        try {
            logger.info('世界树插件', '开始初始化...');
            
            this.config = config;
            
            // 验证必要配置
            if (!config.AI_API_KEY) {
                logger.warning('世界树插件', 'AI_API_KEY未配置，心理活动生成功能将被禁用');
            }
            
            // 初始化世界树系统
            this.worldTree = new WorldTreeSystem(this.pluginDir, config);
            await this.worldTree.initializeDatabase();
            
            this.isInitialized = true;
            logger.success('世界树插件', '初始化完成');
            
            return { success: true, message: '世界树插件初始化成功' };
            
        } catch (error) {
            logger.error('世界树插件', `初始化失败: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 处理VCP工具调用
     */
    async execute(args) {
        try {
            if (!this.isInitialized) {
                throw new Error('插件未初始化');
            }
            
            // 解析参数
            let params;
            try {
                params = typeof args === 'string' ? JSON.parse(args) : args;
            } catch (e) {
                throw new Error('参数格式错误，需要JSON格式');
            }
            
            const { action, ...actionParams } = params;
            
            if (!action) {
                throw new Error('缺少action参数');
            }
            
            logger.info('世界树插件', `执行操作: ${action}`, actionParams);
            
            // 根据操作类型分发处理
            switch (action) {
                case 'create_character':
                    return await this.handleCreateCharacter(actionParams);
                    
                case 'get_characters':
                    return await this.handleGetCharacters(actionParams);
                    
                case 'get_character_context':
                    return await this.handleGetCharacterContext(actionParams);
                    
                case 'add_schedule':
                    return await this.handleAddSchedule(actionParams);
                    
                case 'update_character_state':
                    return await this.handleUpdateCharacterState(actionParams);
                    
                case 'generate_psychology':
                    return await this.handleGeneratePsychology(actionParams);
                    
                case 'get_system_message':
                    return await this.handleGetSystemMessage(actionParams);
                    
                default:
                    throw new Error(`未知操作: ${action}`);
            }
            
        } catch (error) {
            logger.error('世界树插件', `执行失败: ${error.message}`);
            return {
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }

    /**
     * 处理创建角色
     */
    async handleCreateCharacter(params) {
        const { character_name, time_structure, description } = params;
        
        if (!character_name || !time_structure) {
            throw new Error('缺少必要参数: character_name, time_structure');
        }
        
        const result = await this.worldTree.createCharacter(character_name, time_structure, description);
        
        // 清除相关缓存
        this.clearSystemMessageCache(character_name);
        
        return {
            success: result.success,
            message: result.message || result.error,
            character_name,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 处理获取角色列表
     */
    async handleGetCharacters(params) {
        const result = await this.worldTree.getCharacters();
        
        return {
            success: result.success,
            characters: result.characters || [],
            count: result.characters?.length || 0,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 处理获取角色上下文
     */
    async handleGetCharacterContext(params) {
        const { character_name, include_schedule = true, include_psychology = true } = params;
        
        if (!character_name) {
            throw new Error('缺少必要参数: character_name');
        }
        
        const result = await this.worldTree.getCharacterContext(
            character_name, 
            include_schedule, 
            include_psychology
        );
        
        return {
            success: result.success,
            context: result.context,
            error: result.error,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 处理添加日程
     */
    async handleAddSchedule(params) {
        const { character_name, ...scheduleData } = params;
        
        if (!character_name) {
            throw new Error('缺少必要参数: character_name');
        }
        
        const result = await this.worldTree.addSchedule(character_name, scheduleData);
        
        // 清除相关缓存
        this.clearSystemMessageCache(character_name);
        
        return {
            success: result.success,
            message: result.message || result.error,
            character_name,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 处理更新角色状态
     */
    async handleUpdateCharacterState(params) {
        const { character_name, ...stateData } = params;
        
        if (!character_name) {
            throw new Error('缺少必要参数: character_name');
        }
        
        const result = await this.worldTree.updateCharacterState(character_name, stateData);
        
        // 清除相关缓存
        this.clearSystemMessageCache(character_name);
        
        return {
            success: result.success,
            message: result.message || result.error,
            character_name,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 处理生成心理活动
     */
    async handleGeneratePsychology(params) {
        const { character_name, conversation_context, memory_data } = params;
        
        if (!character_name) {
            throw new Error('缺少必要参数: character_name');
        }
        
        const result = await this.worldTree.generatePsychology(
            character_name, 
            conversation_context, 
            memory_data
        );
        
        // 清除相关缓存
        this.clearSystemMessageCache(character_name);
        
        return {
            success: result.success,
            psychology: result.psychology,
            message: result.message || result.error,
            character_name,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 处理获取系统消息
     */
    async handleGetSystemMessage(params) {
        const { character_name } = params;
        
        if (!character_name) {
            throw new Error('缺少必要参数: character_name');
        }
        
        // 检查缓存
        const cached = this.getSystemMessageFromCache(character_name);
        if (cached) {
            return {
                success: true,
                system_message: cached,
                from_cache: true,
                character_name,
                timestamp: new Date().toISOString()
            };
        }
        
        // 生成新的系统消息
        const systemMessage = await this.worldTree.generateSystemMessage(character_name);
        
        // 缓存结果
        this.cacheSystemMessage(character_name, systemMessage);
        
        return {
            success: true,
            system_message: systemMessage,
            from_cache: false,
            character_name,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 缓存系统消息
     */
    cacheSystemMessage(characterName, message) {
        this.systemMessageCache.set(characterName, {
            message,
            timestamp: Date.now()
        });
    }

    /**
     * 从缓存获取系统消息
     */
    getSystemMessageFromCache(characterName) {
        const cached = this.systemMessageCache.get(characterName);
        if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
            return cached.message;
        }
        return null;
    }

    /**
     * 清除系统消息缓存
     */
    clearSystemMessageCache(characterName) {
        if (characterName) {
            this.systemMessageCache.delete(characterName);
        } else {
            this.systemMessageCache.clear();
        }
    }

    /**
     * 获取Agent系统消息（供server.js调用）
     */
    async getAgentSystemMessage(agentName, userId = null) {
        try {
            if (!this.isInitialized) {
                return '';
            }

            // 检查缓存
            const cacheKey = `${agentName}_${userId || 'global'}`;
            const cached = this.getSystemMessageFromCache(cacheKey);
            if (cached) {
                return cached;
            }

            // 生成新的系统消息
            const systemMessage = await this.worldTree.generateSystemMessage(agentName, userId);

            // 缓存结果
            this.cacheSystemMessage(cacheKey, systemMessage);

            return systemMessage;

        } catch (error) {
            logger.error('世界树插件', `获取Agent系统消息失败: ${error.message}`);
            return '';
        }
    }

    /**
     * 清理资源
     */
    async cleanup() {
        try {
            if (this.worldTree) {
                await this.worldTree.close();
            }
            this.clearSystemMessageCache();
            logger.info('世界树插件', '资源清理完成');
        } catch (error) {
            logger.error('世界树插件', `资源清理失败: ${error.message}`);
        }
    }

    /**
     * VCP插件注册方法（用于service类型插件）
     */
    registerRoutes(app, config, projectBasePath) {
        // 这是service类型插件的注册方法
        // 世界树插件主要通过全局对象提供服务，不需要注册HTTP路由
        logger.info('世界树插件', '世界树插件已注册为服务插件');
    }
}

// 导出插件类和实例
module.exports = WorldTreePlugin;

// 如果作为VCP工具被直接调用，处理命令行参数
if (require.main === module) {
    (async () => {
        try {
            // 从命令行参数获取输入
            const input = process.argv[2];
            if (!input) {
                console.log(JSON.stringify({
                    success: false,
                    error: '缺少输入参数'
                }));
                process.exit(1);
            }

            // 创建插件实例
            const plugin = new WorldTreePlugin();

            // 从环境变量加载配置
            const config = {
                DebugMode: (process.env.DebugMode || 'false').toLowerCase() === 'true',
                AI_API_URL: process.env.AI_API_URL || 'https://api.openai.com/v1',
                AI_API_KEY: process.env.AI_API_KEY,
                AI_MODEL: process.env.AI_MODEL || 'gpt-4o-mini',
                PSYCHOLOGY_GENERATION_ENABLED: (process.env.PSYCHOLOGY_GENERATION_ENABLED || 'true').toLowerCase() === 'true',
                MAX_PSYCHOLOGY_LENGTH: parseInt(process.env.MAX_PSYCHOLOGY_LENGTH || '500'),
                SCHEDULE_UPDATE_INTERVAL: parseInt(process.env.SCHEDULE_UPDATE_INTERVAL || '60')
            };

            // 初始化插件
            await plugin.initialize(config);

            // 执行操作
            const result = await plugin.execute(input);

            // 输出结果
            console.log(JSON.stringify(result, null, 2));

            // 清理资源
            await plugin.cleanup();

        } catch (error) {
            console.log(JSON.stringify({
                success: false,
                error: error.message,
                timestamp: new Date().toISOString()
            }));
            process.exit(1);
        }
    })();
}
